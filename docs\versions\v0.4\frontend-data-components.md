# AI-FDB v0.4 - 前端数据组件

## 概述

前端数据组件模块提供完整的数据管理用户界面，包括数据表格、导入向导、编辑表单、搜索过滤等核心组件。所有组件基于Vue 3和Element Plus构建，提供响应式设计和良好的用户体验。

## 🎯 设计目标

- **响应式设计** - 适配桌面端和移动端设备
- **组件化架构** - 可复用的模块化组件设计
- **用户友好** - 直观的操作界面和流畅的交互体验
- **性能优化** - 虚拟滚动、懒加载等性能优化技术
- **可访问性** - 支持键盘导航和屏幕阅读器
- **国际化** - 支持多语言切换

## 🏗️ 组件架构

### 组件层次结构
```
DataWorkspace (工作空间)
├── DataTableList (数据表列表)
├── DataRecordList (数据记录列表)
│   ├── DataTable (数据表格)
│   ├── SearchPanel (搜索面板)
│   ├── FilterPanel (筛选面板)
│   └── BatchActionBar (批量操作栏)
├── ImportWizard (导入向导)
│   ├── FileUploadStep (文件上传步骤)
│   ├── FieldMappingStep (字段映射步骤)
│   ├── DataPreviewStep (数据预览步骤)
│   └── ImportResultStep (导入结果步骤)
├── RecordEditor (记录编辑器)
├── AIExtractionPanel (AI抽取面板)
└── AuditLogViewer (操作日志查看器)
```

## 🎨 核心组件

### 1. DataRecordList - 数据记录列表
主要的数据展示和管理组件，提供表格展示、搜索、排序、分页等功能。

```vue
<template>
  <div class="data-record-list">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="search-section">
        <el-input
          v-model="searchText"
          placeholder="搜索记录..."
          prefix-icon="Search"
          @input="handleSearch"
          clearable
        />
        <el-button @click="showAdvancedSearch = true">
          高级搜索
        </el-button>
      </div>
      
      <div class="action-section">
        <el-button @click="showCreateDialog = true" type="primary">
          <el-icon><Plus /></el-icon>
          添加记录
        </el-button>
        <el-button @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="showAIExtractDialog = true">
          <el-icon><MagicStick /></el-icon>
          AI抽取
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="records"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        height="600"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" fixed="left" />
        
        <el-table-column
          v-for="field in visibleFields"
          :key="field.fieldName"
          :prop="field.fieldName"
          :label="field.displayName || field.fieldName"
          :width="getColumnWidth(field)"
          :sortable="field.sortable !== false ? 'custom' : false"
          :show-overflow-tooltip="true"
        >
          <template #default="{ row }">
            <RecordFieldDisplay
              :field="field"
              :value="row.recordData[field.fieldName]"
              :editable="canEdit && field.editable !== false"
              @update="handleFieldUpdate(row, field, $event)"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button @click="editRecord(row)" size="small" type="text">
              编辑
            </el-button>
            <el-button @click="viewHistory(row)" size="small" type="text">
              历史
            </el-button>
            <el-button @click="deleteRecord(row)" size="small" type="text" class="danger">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 批量操作栏 -->
    <BatchActionBar
      v-if="selectedRecords.length > 0"
      :selected-count="selectedRecords.length"
      @batch-delete="handleBatchDelete"
      @batch-export="handleBatchExport"
      @clear-selection="clearSelection"
    />

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>
```

### 2. ImportWizard - 导入向导
分步骤的数据导入界面，引导用户完成文件上传、字段映射、数据预览和导入执行。

```vue
<template>
  <el-dialog v-model="visible" title="数据导入" width="900px" :close-on-click-modal="false">
    <el-steps :active="currentStep" align-center class="import-steps">
      <el-step title="上传文件" icon="Upload" />
      <el-step title="字段映射" icon="Connection" />
      <el-step title="数据预览" icon="View" />
      <el-step title="导入结果" icon="CircleCheck" />
    </el-steps>
    
    <div class="step-content">
      <!-- 步骤1: 文件上传 -->
      <FileUploadStep
        v-if="currentStep === 0"
        v-model:file="uploadFile"
        @file-analyzed="handleFileAnalyzed"
      />
      
      <!-- 步骤2: 字段映射 -->
      <FieldMappingStep
        v-if="currentStep === 1"
        :file-headers="fileHeaders"
        :table-fields="tableFields"
        v-model:mapping="fieldMapping"
        @mapping-changed="handleMappingChanged"
      />
      
      <!-- 步骤3: 数据预览 -->
      <DataPreviewStep
        v-if="currentStep === 2"
        :preview-data="previewData"
        :field-mapping="fieldMapping"
        :validation-errors="validationErrors"
        @validation-completed="handleValidationCompleted"
      />
      
      <!-- 步骤4: 导入结果 -->
      <ImportResultStep
        v-if="currentStep === 3"
        :job-id="importJobId"
        :import-status="importStatus"
        @import-completed="handleImportCompleted"
      />
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
        <el-button
          v-if="currentStep < 3"
          type="primary"
          @click="nextStep"
          :disabled="!canProceed"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 2"
          type="success"
          @click="startImport"
          :loading="importing"
        >
          开始导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
```

### 3. RecordFieldDisplay - 字段显示组件
根据字段类型智能显示和编辑数据的通用组件。

```vue
<template>
  <div class="record-field-display">
    <!-- 文本类型 -->
    <template v-if="field.fieldType === 'text'">
      <el-input
        v-if="isEditing"
        v-model="editValue"
        @blur="handleBlur"
        @keyup.enter="handleBlur"
        size="small"
      />
      <span v-else @click="startEdit" class="field-value">
        {{ displayValue }}
      </span>
    </template>
    
    <!-- 数字类型 -->
    <template v-if="field.fieldType === 'number'">
      <el-input-number
        v-if="isEditing"
        v-model="editValue"
        @blur="handleBlur"
        size="small"
        :precision="field.precision || 0"
      />
      <span v-else @click="startEdit" class="field-value number">
        {{ formatNumber(value) }}
      </span>
    </template>
    
    <!-- 日期类型 -->
    <template v-if="field.fieldType === 'date'">
      <el-date-picker
        v-if="isEditing"
        v-model="editValue"
        @blur="handleBlur"
        size="small"
        type="date"
      />
      <span v-else @click="startEdit" class="field-value date">
        {{ formatDate(value) }}
      </span>
    </template>
    
    <!-- 布尔类型 -->
    <template v-if="field.fieldType === 'boolean'">
      <el-switch
        v-if="isEditing"
        v-model="editValue"
        @change="handleBlur"
        size="small"
      />
      <el-tag v-else @click="startEdit" :type="value ? 'success' : 'info'" size="small">
        {{ value ? '是' : '否' }}
      </el-tag>
    </template>
    
    <!-- 选择类型 -->
    <template v-if="field.fieldType === 'select'">
      <el-select
        v-if="isEditing"
        v-model="editValue"
        @blur="handleBlur"
        size="small"
      >
        <el-option
          v-for="option in field.options"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>
      <el-tag v-else @click="startEdit" size="small">
        {{ getOptionLabel(value) }}
      </el-tag>
    </template>
    
    <!-- 文件类型 -->
    <template v-if="field.fieldType === 'file'">
      <el-link v-if="value" :href="value" target="_blank" type="primary">
        <el-icon><Document /></el-icon>
        查看文件
      </el-link>
      <span v-else class="field-value empty">无文件</span>
    </template>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { Document } from '@element-plus/icons-vue'

export default {
  name: 'RecordFieldDisplay',
  components: { Document },
  props: {
    field: {
      type: Object,
      required: true
    },
    value: {
      type: [String, Number, Boolean, Date, Array, Object],
      default: null
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update'],
  setup(props, { emit }) {
    const isEditing = ref(false)
    const editValue = ref(props.value)
    
    const displayValue = computed(() => {
      if (props.value === null || props.value === undefined) {
        return '-'
      }
      return String(props.value)
    })
    
    const startEdit = () => {
      if (!props.editable) return
      isEditing.value = true
      editValue.value = props.value
    }
    
    const handleBlur = () => {
      isEditing.value = false
      if (editValue.value !== props.value) {
        emit('update', editValue.value)
      }
    }
    
    const formatNumber = (value) => {
      if (value === null || value === undefined) return '-'
      return Number(value).toLocaleString()
    }
    
    const formatDate = (value) => {
      if (!value) return '-'
      return new Date(value).toLocaleDateString()
    }
    
    const getOptionLabel = (value) => {
      const option = props.field.options?.find(opt => opt.value === value)
      return option?.label || value || '-'
    }
    
    watch(() => props.value, (newValue) => {
      editValue.value = newValue
    })
    
    return {
      isEditing,
      editValue,
      displayValue,
      startEdit,
      handleBlur,
      formatNumber,
      formatDate,
      getOptionLabel
    }
  }
}
</script>
```

### 4. AIExtractionPanel - AI抽取面板
提供AI数据抽取功能的用户界面。

```vue
<template>
  <div class="ai-extraction-panel">
    <el-card header="AI数据抽取" class="extraction-card">
      <el-form :model="extractionForm" label-width="100px">
        <el-form-item label="源文本">
          <el-input 
            v-model="extractionForm.sourceContent"
            type="textarea"
            :rows="8"
            placeholder="请输入要抽取数据的文本内容..."
            show-word-limit
            maxlength="5000"
          />
        </el-form-item>
        
        <el-form-item label="抽取模式">
          <el-radio-group v-model="extractionForm.mode">
            <el-radio label="auto">自动抽取</el-radio>
            <el-radio label="guided">引导抽取</el-radio>
            <el-radio label="template">模板抽取</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="extractData"
            :loading="extracting"
            :disabled="!extractionForm.sourceContent.trim()"
          >
            <el-icon><MagicStick /></el-icon>
            开始抽取
          </el-button>
          <el-button @click="clearResults">清空结果</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 抽取结果 -->
    <el-card header="抽取结果" v-if="extractionResult" class="result-card">
      <div class="extraction-results">
        <div 
          v-for="(value, field) in extractionResult.extractedData" 
          :key="field"
          class="result-item"
        >
          <div class="field-info">
            <span class="field-name">{{ getFieldDisplayName(field) }}</span>
            <el-tag 
              :type="getConfidenceType(extractionResult.confidenceScores[field])"
              size="small"
            >
              置信度: {{ (extractionResult.confidenceScores[field] * 100).toFixed(1) }}%
            </el-tag>
          </div>
          <div class="field-value">
            <el-input 
              v-model="extractionResult.extractedData[field]"
              @change="updateExtractedValue(field, $event)"
            />
          </div>
        </div>
      </div>
      
      <div class="result-actions">
        <el-button type="success" @click="acceptAllResults">
          <el-icon><Check /></el-icon>
          接受所有结果
        </el-button>
        <el-button @click="saveAsTemplate">
          <el-icon><Document /></el-icon>
          保存为模板
        </el-button>
      </div>
    </el-card>
  </div>
</template>
```

## 🎨 样式设计

### 主题色彩
```scss
// 主色调
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 背景色
$bg-color: #F5F7FA;
$card-bg-color: #FFFFFF;
$table-header-bg: #FAFAFA;

// 文字色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #C0C4CC;
```

### 响应式断点
```scss
// 响应式断点
$mobile: 768px;
$tablet: 1024px;
$desktop: 1200px;

// 响应式混入
@mixin mobile {
  @media (max-width: $mobile) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: $tablet) {
    @content;
  }
}
```

## 🧪 测试策略

### 组件测试
- 单元测试覆盖所有组件
- 交互测试验证用户操作
- 响应式测试确保多设备兼容
- 可访问性测试支持无障碍访问

### 集成测试
- 组件间通信测试
- 数据流测试
- API集成测试
- 端到端测试

### 性能测试
- 组件渲染性能测试
- 大数据量处理测试
- 内存泄漏检测
- 加载时间优化测试

---

**相关文档**:
- [数据导入设计](./data-import-design.md)
- [数据管理实现](./data-management-implementation.md)
- [AI抽取服务](./ai-extraction-service.md)
