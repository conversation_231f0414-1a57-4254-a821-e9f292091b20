@echo off
chcp 65001 >nul
echo ========================================
echo 网络共享状态检查工具
echo 本地IP: *************
echo ========================================
echo.

echo [检查1] 当前网络配置
echo 计算机名: %COMPUTERNAME%
echo 用户名: %USERNAME%
echo 域: %USERDOMAIN%
echo.

echo [检查2] IP配置信息
ipconfig | findstr /C:"IPv4" /C:"子网掩码" /C:"默认网关"
echo.

echo [检查3] 网络连通性测试
echo 正在测试网关连接...
ping -n 2 ************* >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 网关 ************* 连接正常
) else (
    echo ✗ 网关 ************* 连接失败
)

echo 正在测试本地回环...
ping -n 2 ************* >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 本地IP ************* 可达
) else (
    echo ✗ 本地IP ************* 不可达
)
echo.

echo [检查4] SMB服务状态
sc query lanmanserver | findstr STATE
if %errorLevel% equ 0 (
    echo ✓ SMB服务正在运行
) else (
    echo ✗ SMB服务未运行
)
echo.

echo [检查5] 防火墙状态
netsh advfirewall show allprofiles state | findstr "状态\|State"
echo.

echo [检查6] 网络发现状态
netsh advfirewall firewall show rule name="网络发现" | findstr "已启用\|Enabled" >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 网络发现已启用
) else (
    echo ✗ 网络发现未启用
)

netsh advfirewall firewall show rule name="文件和打印机共享" | findstr "已启用\|Enabled" >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ 文件和打印机共享已启用
) else (
    echo ✗ 文件和打印机共享未启用
)
echo.

echo [检查7] 当前共享资源
net share
echo.

echo [检查8] 网络邻居发现
echo 正在扫描局域网设备...
for /L %%i in (1,1,10) do (
    ping -n 1 -w 100 192.168.124.%%i >nul 2>&1
    if not errorlevel 1 (
        echo ✓ 发现设备: 192.168.124.%%i
    )
)
echo.

echo ========================================
echo 快速修复建议
echo ========================================
echo.
echo 如果发现问题，可以尝试以下解决方案：
echo.
echo 1. 启用网络发现和文件共享：
echo    netsh advfirewall firewall set rule group="网络发现" new enable=Yes
echo    netsh advfirewall firewall set rule group="文件和打印机共享" new enable=Yes
echo.
echo 2. 启动SMB服务：
echo    net start server
echo.
echo 3. 设置网络为专用：
echo    运行PowerShell命令：
echo    Get-NetConnectionProfile ^| Set-NetConnectionProfile -NetworkCategory Private
echo.
echo 4. 创建共享文件夹：
echo    net share 共享名=C:\要共享的路径 /grant:everyone,full
echo.
echo 5. 重启网络服务：
echo    net stop server && net start server
echo.
echo 访问测试命令：
echo - 本机测试: net view \\%COMPUTERNAME%
echo - IP测试: net view \\*************
echo - 远程测试: 在其他设备运行 net view \\*************
echo.
pause