@echo off
echo ========================================
echo Windows LAN Discovery and Sharing Setup
echo Local IP: *************
echo ========================================
echo.

REM Check administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: Administrator privileges required
    echo Please right-click Command Prompt and select "Run as administrator"
    pause
    exit /b 1
)

echo [1/6] Enabling Network Discovery...
netsh advfirewall firewall set rule group="Network Discovery" new enable=Yes
if %errorLevel% equ 0 (
    echo + Network Discovery enabled
) else (
    echo - Network Discovery failed
)
echo.

echo [2/6] Enabling File and Printer Sharing...
netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=Yes
if %errorLevel% equ 0 (
    echo + File and Printer Sharing enabled
) else (
    echo - File and Printer Sharing failed
)
echo.

echo [3/6] Setting network to Private...
powershell -Command "Get-NetConnectionProfile | Set-NetConnectionProfile -NetworkCategory Private"
if %errorLevel% equ 0 (
    echo + Network set to Private
) else (
    echo - Network setting failed
)
echo.

echo [4/6] Starting SMB Service...
sc config lanmanserver start= auto
net start server
if %errorLevel% equ 0 (
    echo + SMB Service started
) else (
    echo - SMB Service failed
)
echo.

echo [5/6] Configuring Firewall Rules...
REM Allow SMB traffic
netsh advfirewall firewall add rule name="SMB-In" dir=in action=allow protocol=TCP localport=445
netsh advfirewall firewall add rule name="SMB-Out" dir=out action=allow protocol=TCP localport=445
REM Allow NetBIOS traffic
netsh advfirewall firewall add rule name="NetBIOS-In" dir=in action=allow protocol=TCP localport=139
netsh advfirewall firewall add rule name="NetBIOS-Out" dir=out action=allow protocol=TCP localport=139
REM Allow ICMP ping
netsh advfirewall firewall add rule name="ICMP-In" dir=in action=allow protocol=icmpv4:8,any
echo + Firewall rules configured
echo.

echo [6/6] Verifying Network Settings...
echo Checking network connection...
ping -n 1 ************* >nul 2>&1
if %errorLevel% equ 0 (
    echo + Gateway connection OK
) else (
    echo - Gateway connection may have issues
)

echo.
echo ========================================
echo Configuration Complete!
echo ========================================
echo.
echo Current Computer Information:
echo - Computer Name: %COMPUTERNAME%
echo - Local IP: *************
echo - Network Path: \\%COMPUTERNAME%
echo - IP Path: \\*************
echo.
echo Other devices in LAN can access via:
echo 1. File Explorer address bar: \\%COMPUTERNAME%
echo 2. File Explorer address bar: \\*************
echo 3. Run dialog (Win+R) with above paths
echo.
echo To share specific folders:
echo 1. Right-click folder to share
echo 2. Select "Properties" - "Sharing" tab
echo 3. Click "Advanced Sharing" and set permissions
echo.
echo Note: Ensure all devices are in same LAN (192.168.124.x)
echo.
pause