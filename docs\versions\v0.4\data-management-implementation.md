# AI-FDB v0.4 - 数据管理实现

## 概述

数据管理模块负责实现数据的增删改查、搜索、排序、分页等基础功能，为用户提供完整的数据操作体验。本模块在v0.3表结构设计基础上，实现对实际数据记录的全生命周期管理。

## 🎯 功能目标

- **数据展示** - 响应式表格展示，支持大数据量分页浏览
- **数据编辑** - 在线编辑、批量操作、实时验证
- **搜索过滤** - 全文搜索、字段搜索、高级筛选
- **排序分页** - 多字段排序、灵活分页、性能优化
- **批量操作** - 批量删除、批量修改、批量导出
- **操作历史** - 数据变更记录、版本管理、回滚功能

## 🏗️ 系统架构

### 数据管理架构
```
前端组件层 → 控制器层 → 服务层 → 数据访问层 → 数据库层
     ↓          ↓        ↓         ↓          ↓
  Vue组件    Controller  Service    Repository  MySQL
     ↓          ↓        ↓         ↓          ↓
  用户交互    API接口   业务逻辑   数据操作    数据存储
```

### 核心组件
1. **DataRecordController** - 数据记录API控制器
2. **DataRecordService** - 数据记录业务服务
3. **DataValidationEngine** - 数据验证引擎
4. **SearchEngine** - 搜索引擎
5. **BatchOperationService** - 批量操作服务
6. **AuditLogService** - 操作日志服务

## 📊 数据库设计

### 数据记录表 (data_records)
```sql
CREATE TABLE data_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    record_data JSON NOT NULL COMMENT '记录数据',
    record_hash VARCHAR(64) COMMENT '数据哈希值，用于去重',
    status ENUM('draft', 'published', 'archived') DEFAULT 'published',
    source_type ENUM('manual', 'import', 'ai_extract') DEFAULT 'manual',
    source_info JSON COMMENT '数据来源信息',
    validation_status ENUM('pending', 'valid', 'invalid') DEFAULT 'pending',
    validation_errors JSON COMMENT '验证错误信息',
    version INT DEFAULT 1,
    created_by BIGINT NOT NULL,
    updated_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_status (status),
    INDEX idx_source_type (source_type),
    INDEX idx_validation_status (validation_status),
    INDEX idx_record_hash (record_hash),
    INDEX idx_created_at (created_at),
    FULLTEXT INDEX ft_record_data (record_data)
);
```

### 操作日志表 (data_audit_logs)
```sql
CREATE TABLE data_audit_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    record_id BIGINT,
    operation_type ENUM('create', 'update', 'delete', 'batch_update', 'batch_delete') NOT NULL,
    old_data JSON COMMENT '操作前数据',
    new_data JSON COMMENT '操作后数据',
    changed_fields JSON COMMENT '变更字段列表',
    operation_context JSON COMMENT '操作上下文',
    user_id BIGINT NOT NULL,
    user_ip VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (record_id) REFERENCES data_records(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_record_id (record_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);
```

## 🔧 后端实现

### 数据记录控制器
```java
@RestController
@RequestMapping("/api/tables/{tableId}/records")
@PreAuthorize("hasRole('USER')")
public class DataRecordController {

    @Autowired
    private DataRecordService recordService;

    @GetMapping
    @PreAuthorize("@tablePermissionService.hasPermission(#tableId, authentication.name, 'VIEW')")
    public ResponseEntity<Page<DataRecord>> getRecords(
        @PathVariable Long tableId,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "20") int size,
        @RequestParam(required = false) String search,
        @RequestParam(required = false) String sortBy,
        @RequestParam(defaultValue = "desc") String sortOrder,
        @RequestParam(required = false) Map<String, String> filters) {
        
        PageRequest pageRequest = PageRequest.of(page, size);
        Page<DataRecord> records = recordService.getTableRecords(
            tableId, pageRequest, search, sortBy, sortOrder, filters);
        return ResponseEntity.ok(records);
    }

    @PostMapping
    @PreAuthorize("@tablePermissionService.hasPermission(#tableId, authentication.name, 'EDIT')")
    public ResponseEntity<?> createRecord(
        @PathVariable Long tableId,
        @RequestBody CreateRecordRequest request,
        Authentication auth) {
        try {
            DataRecord record = recordService.createRecord(tableId, request, auth.getName());
            return ResponseEntity.ok(record);
        } catch (ValidationException e) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse("数据验证失败", e.getErrors()));
        }
    }

    @PutMapping("/{recordId}")
    @PreAuthorize("@tablePermissionService.hasPermission(#tableId, authentication.name, 'EDIT')")
    public ResponseEntity<?> updateRecord(
        @PathVariable Long tableId,
        @PathVariable Long recordId,
        @RequestBody UpdateRecordRequest request,
        Authentication auth) {
        try {
            DataRecord record = recordService.updateRecord(tableId, recordId, request, auth.getName());
            return ResponseEntity.ok(record);
        } catch (ValidationException e) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse("数据验证失败", e.getErrors()));
        }
    }

    @DeleteMapping("/{recordId}")
    @PreAuthorize("@tablePermissionService.hasPermission(#tableId, authentication.name, 'EDIT')")
    public ResponseEntity<?> deleteRecord(
        @PathVariable Long tableId,
        @PathVariable Long recordId,
        Authentication auth) {
        recordService.deleteRecord(tableId, recordId, auth.getName());
        return ResponseEntity.ok(new ApiResponse("删除成功"));
    }

    @PostMapping("/batch")
    @PreAuthorize("@tablePermissionService.hasPermission(#tableId, authentication.name, 'EDIT')")
    public ResponseEntity<?> batchOperation(
        @PathVariable Long tableId,
        @RequestBody BatchOperationRequest request,
        Authentication auth) {
        try {
            BatchOperationResult result = recordService.batchOperation(tableId, request, auth.getName());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse("批量操作失败: " + e.getMessage()));
        }
    }
}
```

### 数据记录服务
```java
@Service
@Transactional
public class DataRecordService {

    @Autowired
    private DataRecordRepository recordRepository;
    
    @Autowired
    private DataValidationEngine validationEngine;
    
    @Autowired
    private AuditLogService auditLogService;

    public Page<DataRecord> getTableRecords(Long tableId, PageRequest pageRequest, 
            String search, String sortBy, String sortOrder, Map<String, String> filters) {
        
        Specification<DataRecord> spec = Specification.where(
            DataRecordSpecifications.belongsToTable(tableId))
            .and(DataRecordSpecifications.hasStatus(RecordStatus.PUBLISHED));
            
        if (StringUtils.hasText(search)) {
            spec = spec.and(DataRecordSpecifications.containsText(search));
        }
        
        if (filters != null && !filters.isEmpty()) {
            spec = spec.and(DataRecordSpecifications.matchesFilters(filters));
        }
        
        Sort sort = createSort(sortBy, sortOrder);
        PageRequest sortedPageRequest = PageRequest.of(
            pageRequest.getPageNumber(), 
            pageRequest.getPageSize(), 
            sort);
            
        return recordRepository.findAll(spec, sortedPageRequest);
    }

    public DataRecord createRecord(Long tableId, CreateRecordRequest request, String username) {
        DataTable table = getTableById(tableId);
        
        // 数据验证
        ValidationResult validation = validationEngine.validateRecord(table, request.getData());
        if (validation.hasErrors()) {
            throw new ValidationException("数据验证失败", validation.getErrors());
        }
        
        // 创建记录
        DataRecord record = new DataRecord();
        record.setTableId(tableId);
        record.setRecordData(request.getData());
        record.setRecordHash(calculateHash(request.getData()));
        record.setSourceType(RecordSourceType.MANUAL);
        record.setValidationStatus(ValidationStatus.VALID);
        record.setCreatedBy(getUserId(username));
        
        DataRecord savedRecord = recordRepository.save(record);
        
        // 记录操作日志
        auditLogService.logCreate(tableId, savedRecord, username);
        
        return savedRecord;
    }

    public DataRecord updateRecord(Long tableId, Long recordId, UpdateRecordRequest request, String username) {
        DataRecord record = recordRepository.findByIdAndTableId(recordId, tableId)
            .orElseThrow(() -> new EntityNotFoundException("记录不存在"));
            
        DataTable table = getTableById(tableId);
        
        // 保存原始数据用于审计
        Map<String, Object> oldData = new HashMap<>(record.getRecordData());
        
        // 数据验证
        ValidationResult validation = validationEngine.validateRecord(table, request.getData());
        if (validation.hasErrors()) {
            throw new ValidationException("数据验证失败", validation.getErrors());
        }
        
        // 更新记录
        record.setRecordData(request.getData());
        record.setRecordHash(calculateHash(request.getData()));
        record.setValidationStatus(ValidationStatus.VALID);
        record.setUpdatedBy(getUserId(username));
        record.setVersion(record.getVersion() + 1);
        
        DataRecord savedRecord = recordRepository.save(record);
        
        // 记录操作日志
        auditLogService.logUpdate(tableId, savedRecord, oldData, request.getData(), username);
        
        return savedRecord;
    }

    public void deleteRecord(Long tableId, Long recordId, String username) {
        DataRecord record = recordRepository.findByIdAndTableId(recordId, tableId)
            .orElseThrow(() -> new EntityNotFoundException("记录不存在"));
            
        // 记录操作日志
        auditLogService.logDelete(tableId, record, username);
        
        recordRepository.delete(record);
    }

    @Async
    public CompletableFuture<BatchOperationResult> batchOperation(
            Long tableId, BatchOperationRequest request, String username) {
        
        BatchOperationResult result = new BatchOperationResult();
        
        switch (request.getOperation()) {
            case DELETE:
                result = batchDelete(tableId, request.getRecordIds(), username);
                break;
            case UPDATE:
                result = batchUpdate(tableId, request.getRecordIds(), request.getData(), username);
                break;
            default:
                throw new IllegalArgumentException("不支持的批量操作类型");
        }
        
        return CompletableFuture.completedFuture(result);
    }
}
```

## 🎨 前端实现

### 数据记录列表组件
```vue
<template>
  <div class="data-record-list">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="search-area">
        <el-input
          v-model="searchText"
          placeholder="搜索记录..."
          prefix-icon="Search"
          @input="handleSearch"
          style="width: 300px"
        />
        <el-button @click="showAdvancedSearch = true">高级搜索</el-button>
      </div>
      
      <div class="action-area">
        <el-button @click="showCreateDialog = true" type="primary">
          <el-icon><Plus /></el-icon>
          添加记录
        </el-button>
        <el-button @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-dropdown>
          <el-button>
            更多操作 <el-icon><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="exportData">导出数据</el-dropdown-item>
              <el-dropdown-item @click="downloadTemplate">下载模板</el-dropdown-item>
              <el-dropdown-item @click="showAuditLog = true">操作日志</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="records"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          v-for="field in visibleFields"
          :key="field.fieldName"
          :prop="field.fieldName"
          :label="field.displayName || field.fieldName"
          :width="getColumnWidth(field)"
          :sortable="field.sortable !== false ? 'custom' : false"
        >
          <template #default="{ row }">
            <RecordFieldDisplay
              :field="field"
              :value="row.recordData[field.fieldName]"
              :editable="canEdit"
              @update="handleFieldUpdate(row, field, $event)"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button @click="editRecord(row)" size="small" type="text">
              编辑
            </el-button>
            <el-button @click="viewHistory(row)" size="small" type="text">
              历史
            </el-button>
            <el-button @click="deleteRecord(row)" size="small" type="text" class="danger">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 批量操作栏 -->
    <div class="batch-actions" v-if="selectedRecords.length > 0">
      <span>已选择 {{ selectedRecords.length }} 条记录</span>
      <el-button @click="batchDelete" type="danger" size="small">
        批量删除
      </el-button>
      <el-button @click="batchExport" size="small">
        批量导出
      </el-button>
      <el-button @click="clearSelection" size="small">
        取消选择
      </el-button>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>
```

## 🧪 测试用例

### 功能测试
- 数据增删改查功能测试
- 搜索和筛选功能测试
- 排序和分页功能测试
- 批量操作功能测试
- 数据验证功能测试

### 性能测试
- 大数据量加载性能测试
- 搜索响应时间测试
- 批量操作性能测试
- 并发操作压力测试

### 用户体验测试
- 界面响应性测试
- 操作流畅性测试
- 错误提示友好性测试
- 移动端适配测试

---

**相关文档**:
- [数据导入设计](./data-import-design.md)
- [AI抽取服务](./ai-extraction-service.md)
- [前端数据组件](./frontend-data-components.md)
