# AI-FDB v0.5 - 质量控制系统

## 概述

质量控制系统是v0.5版本的重要组成部分，负责确保文档抽取结果的准确性和可靠性。系统通过多维度质量评估、人工校验流程、反馈学习机制，持续提升抽取质量和用户满意度。

## 🎯 功能目标

- **质量评估** - 多维度的抽取结果质量评估
- **人工校验** - 可配置的人工审核流程
- **反馈学习** - 基于用户反馈的持续优化
- **质量监控** - 实时质量指标监控和告警
- **标准管理** - 质量标准和规则的配置管理
- **报告分析** - 质量趋势分析和改进建议

## 🏗️ 系统架构

### 质量控制流程
```
抽取结果 → 质量评估 → 置信度分析 → 人工校验 → 反馈收集 → 学习优化 → 质量报告
    ↓        ↓        ↓         ↓        ↓        ↓        ↓
  结果输入  多维评估  置信度计算  人工审核  用户反馈  模型优化  质量分析
```

### 核心组件
1. **QualityAssessor** - 质量评估器
2. **HumanReviewManager** - 人工审核管理器
3. **FeedbackCollector** - 反馈收集器
4. **LearningEngine** - 学习引擎
5. **QualityMonitor** - 质量监控器
6. **ReportGenerator** - 报告生成器

## 📊 数据库设计

### 质量评估表 (quality_assessments)
```sql
CREATE TABLE quality_assessments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_task_id BIGINT NOT NULL,
    batch_job_id BIGINT NOT NULL,
    overall_score DECIMAL(3,2) COMMENT '总体质量评分 0-1',
    accuracy_score DECIMAL(3,2) COMMENT '准确性评分',
    completeness_score DECIMAL(3,2) COMMENT '完整性评分',
    consistency_score DECIMAL(3,2) COMMENT '一致性评分',
    confidence_score DECIMAL(3,2) COMMENT '置信度评分',
    field_scores JSON COMMENT '各字段质量评分',
    quality_issues JSON COMMENT '质量问题列表',
    assessment_method ENUM('automatic', 'manual', 'hybrid') DEFAULT 'automatic',
    assessed_by BIGINT,
    assessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_task_id) REFERENCES document_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (batch_job_id) REFERENCES batch_jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (assessed_by) REFERENCES users(id),
    INDEX idx_document_task_id (document_task_id),
    INDEX idx_batch_job_id (batch_job_id),
    INDEX idx_overall_score (overall_score)
);
```

### 人工审核表 (human_reviews)
```sql
CREATE TABLE human_reviews (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_task_id BIGINT NOT NULL,
    quality_assessment_id BIGINT NOT NULL,
    review_type ENUM('random_sampling', 'low_confidence', 'quality_issue', 'user_request') NOT NULL,
    original_data JSON COMMENT '原始抽取数据',
    reviewed_data JSON COMMENT '审核后数据',
    review_comments TEXT COMMENT '审核意见',
    corrections JSON COMMENT '修正记录',
    review_status ENUM('pending', 'in_progress', 'completed', 'rejected') DEFAULT 'pending',
    reviewer_id BIGINT,
    assigned_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    review_time INT COMMENT '审核耗时（秒）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_task_id) REFERENCES document_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (quality_assessment_id) REFERENCES quality_assessments(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id),
    INDEX idx_document_task_id (document_task_id),
    INDEX idx_review_status (review_status),
    INDEX idx_reviewer_id (reviewer_id)
);
```

### 反馈记录表 (quality_feedback)
```sql
CREATE TABLE quality_feedback (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_task_id BIGINT NOT NULL,
    feedback_type ENUM('accuracy', 'completeness', 'format', 'other') NOT NULL,
    feedback_rating INT COMMENT '评分 1-5',
    feedback_content TEXT COMMENT '反馈内容',
    suggested_improvements JSON COMMENT '改进建议',
    user_id BIGINT NOT NULL,
    is_processed BOOLEAN DEFAULT FALSE,
    processed_by BIGINT,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_task_id) REFERENCES document_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (processed_by) REFERENCES users(id),
    INDEX idx_document_task_id (document_task_id),
    INDEX idx_feedback_type (feedback_type),
    INDEX idx_is_processed (is_processed)
);
```

## 🔧 技术实现

### 质量控制配置
```yaml
# application.yml
quality:
  assessment:
    enable-automatic: true
    enable-manual: true
    confidence-threshold: 0.8
    quality-threshold: 0.7
    
  review:
    sampling-rate: 0.1 # 10%随机抽样
    low-confidence-threshold: 0.6
    auto-assign-reviewers: true
    max-review-time: 3600 # 1小时
    
  feedback:
    enable-collection: true
    auto-process: true
    min-rating-for-improvement: 3
    
  monitoring:
    alert-threshold: 0.6
    trend-analysis-window: 7d
    report-generation-schedule: "0 0 8 * * MON" # 每周一早8点
```

### 质量评估器
```java
@Service
public class QualityAssessor {

    @Autowired
    private QualityRuleEngine ruleEngine;
    
    @Autowired
    private ConfidenceCalculator confidenceCalculator;

    public QualityAssessment assessQuality(DocumentTask documentTask, 
                                         ExtractionResult extractionResult) {
        try {
            // 1. 计算各维度评分
            double accuracyScore = calculateAccuracyScore(documentTask, extractionResult);
            double completenessScore = calculateCompletenessScore(documentTask, extractionResult);
            double consistencyScore = calculateConsistencyScore(documentTask, extractionResult);
            double confidenceScore = calculateConfidenceScore(extractionResult);
            
            // 2. 计算字段级别评分
            Map<String, Double> fieldScores = calculateFieldScores(documentTask, extractionResult);
            
            // 3. 识别质量问题
            List<QualityIssue> qualityIssues = identifyQualityIssues(
                documentTask, extractionResult, fieldScores);
            
            // 4. 计算总体评分
            double overallScore = calculateOverallScore(
                accuracyScore, completenessScore, consistencyScore, confidenceScore);
            
            // 5. 构建评估结果
            return QualityAssessment.builder()
                .documentTaskId(documentTask.getId())
                .batchJobId(documentTask.getBatchJobId())
                .overallScore(overallScore)
                .accuracyScore(accuracyScore)
                .completenessScore(completenessScore)
                .consistencyScore(consistencyScore)
                .confidenceScore(confidenceScore)
                .fieldScores(fieldScores)
                .qualityIssues(qualityIssues)
                .assessmentMethod(AssessmentMethod.AUTOMATIC)
                .build();
                
        } catch (Exception e) {
            log.error("质量评估失败", e);
            throw new QualityAssessmentException("质量评估失败", e);
        }
    }

    private double calculateAccuracyScore(DocumentTask documentTask, ExtractionResult extractionResult) {
        // 基于规则引擎计算准确性评分
        return ruleEngine.evaluateAccuracy(documentTask, extractionResult);
    }

    private double calculateCompletenessScore(DocumentTask documentTask, ExtractionResult extractionResult) {
        TableSchema schema = getTableSchema(documentTask.getBatchJobId());
        Map<String, Object> extractedData = extractionResult.getExtractedData();
        
        int totalFields = schema.getFields().size();
        int extractedFields = 0;
        
        for (TableField field : schema.getFields()) {
            Object value = extractedData.get(field.getName());
            if (value != null && !value.toString().trim().isEmpty()) {
                extractedFields++;
            }
        }
        
        return (double) extractedFields / totalFields;
    }

    private List<QualityIssue> identifyQualityIssues(DocumentTask documentTask, 
                                                    ExtractionResult extractionResult,
                                                    Map<String, Double> fieldScores) {
        List<QualityIssue> issues = new ArrayList<>();
        
        // 1. 检查低置信度字段
        for (Map.Entry<String, Double> entry : extractionResult.getConfidenceScores().entrySet()) {
            if (entry.getValue() < 0.6) {
                issues.add(new QualityIssue(
                    QualityIssueType.LOW_CONFIDENCE,
                    entry.getKey(),
                    "字段置信度过低: " + entry.getValue()
                ));
            }
        }
        
        // 2. 检查缺失必填字段
        TableSchema schema = getTableSchema(documentTask.getBatchJobId());
        for (TableField field : schema.getFields()) {
            if (field.isRequired()) {
                Object value = extractionResult.getExtractedData().get(field.getName());
                if (value == null || value.toString().trim().isEmpty()) {
                    issues.add(new QualityIssue(
                        QualityIssueType.MISSING_REQUIRED_FIELD,
                        field.getName(),
                        "缺失必填字段"
                    ));
                }
            }
        }
        
        // 3. 检查格式错误
        issues.addAll(validateFieldFormats(extractionResult.getExtractedData(), schema));
        
        return issues;
    }
}
```

### 人工审核管理器
```java
@Service
public class HumanReviewManager {

    @Autowired
    private HumanReviewRepository humanReviewRepository;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private NotificationService notificationService;

    public void triggerReviewIfNeeded(QualityAssessment assessment) {
        // 1. 判断是否需要人工审核
        if (!needsHumanReview(assessment)) {
            return;
        }
        
        // 2. 确定审核类型
        ReviewType reviewType = determineReviewType(assessment);
        
        // 3. 创建审核任务
        HumanReview review = createHumanReview(assessment, reviewType);
        
        // 4. 分配审核员
        assignReviewer(review);
        
        // 5. 发送通知
        notificationService.notifyReviewAssignment(review);
    }

    private boolean needsHumanReview(QualityAssessment assessment) {
        // 1. 总体质量评分过低
        if (assessment.getOverallScore() < getQualityThreshold()) {
            return true;
        }
        
        // 2. 置信度过低
        if (assessment.getConfidenceScore() < getConfidenceThreshold()) {
            return true;
        }
        
        // 3. 存在严重质量问题
        if (hasCriticalQualityIssues(assessment.getQualityIssues())) {
            return true;
        }
        
        // 4. 随机抽样
        if (shouldRandomSample()) {
            return true;
        }
        
        return false;
    }

    private void assignReviewer(HumanReview review) {
        // 1. 获取可用审核员
        List<User> availableReviewers = userService.getAvailableReviewers();
        
        // 2. 根据工作负载和专业领域选择审核员
        User selectedReviewer = selectBestReviewer(availableReviewers, review);
        
        // 3. 分配任务
        review.setReviewerId(selectedReviewer.getId());
        review.setAssignedAt(LocalDateTime.now());
        review.setReviewStatus(ReviewStatus.PENDING);
        
        humanReviewRepository.save(review);
    }

    public HumanReview completeReview(Long reviewId, CompleteReviewRequest request) {
        HumanReview review = humanReviewRepository.findById(reviewId)
            .orElseThrow(() -> new EntityNotFoundException("审核任务不存在"));
        
        // 1. 更新审核结果
        review.setReviewedData(request.getReviewedData());
        review.setReviewComments(request.getComments());
        review.setCorrections(request.getCorrections());
        review.setReviewStatus(ReviewStatus.COMPLETED);
        review.setCompletedAt(LocalDateTime.now());
        
        // 2. 计算审核时间
        if (review.getAssignedAt() != null) {
            long reviewTime = Duration.between(review.getAssignedAt(), review.getCompletedAt()).getSeconds();
            review.setReviewTime((int) reviewTime);
        }
        
        // 3. 保存审核结果
        review = humanReviewRepository.save(review);
        
        // 4. 更新原始抽取结果
        updateExtractionResult(review);
        
        // 5. 触发学习优化
        triggerLearningOptimization(review);
        
        return review;
    }
}
```

### 反馈收集器
```java
@Service
public class FeedbackCollector {

    @Autowired
    private QualityFeedbackRepository feedbackRepository;
    
    @Autowired
    private LearningEngine learningEngine;

    public QualityFeedback collectFeedback(CollectFeedbackRequest request) {
        // 1. 创建反馈记录
        QualityFeedback feedback = new QualityFeedback();
        feedback.setDocumentTaskId(request.getDocumentTaskId());
        feedback.setFeedbackType(request.getFeedbackType());
        feedback.setFeedbackRating(request.getRating());
        feedback.setFeedbackContent(request.getContent());
        feedback.setSuggestedImprovements(request.getImprovements());
        feedback.setUserId(request.getUserId());
        
        feedback = feedbackRepository.save(feedback);
        
        // 2. 自动处理反馈（如果启用）
        if (isAutoProcessEnabled()) {
            processFeedback(feedback);
        }
        
        return feedback;
    }

    public void processFeedback(QualityFeedback feedback) {
        try {
            // 1. 分析反馈内容
            FeedbackAnalysis analysis = analyzeFeedback(feedback);
            
            // 2. 生成改进建议
            List<ImprovementSuggestion> suggestions = generateImprovementSuggestions(analysis);
            
            // 3. 触发学习优化
            learningEngine.learnFromFeedback(feedback, analysis, suggestions);
            
            // 4. 更新处理状态
            feedback.setIsProcessed(true);
            feedback.setProcessedAt(LocalDateTime.now());
            feedbackRepository.save(feedback);
            
        } catch (Exception e) {
            log.error("反馈处理失败", e);
        }
    }

    private FeedbackAnalysis analyzeFeedback(QualityFeedback feedback) {
        FeedbackAnalysis analysis = new FeedbackAnalysis();
        
        // 1. 分析评分趋势
        analysis.setRatingTrend(calculateRatingTrend(feedback.getDocumentTaskId()));
        
        // 2. 提取关键问题
        analysis.setKeyIssues(extractKeyIssues(feedback.getFeedbackContent()));
        
        // 3. 识别改进领域
        analysis.setImprovementAreas(identifyImprovementAreas(feedback));
        
        return analysis;
    }
}
```

### 学习引擎
```java
@Service
public class LearningEngine {

    @Autowired
    private ModelOptimizer modelOptimizer;
    
    @Autowired
    private PromptOptimizer promptOptimizer;
    
    @Autowired
    private RuleOptimizer ruleOptimizer;

    public void learnFromFeedback(QualityFeedback feedback, 
                                 FeedbackAnalysis analysis,
                                 List<ImprovementSuggestion> suggestions) {
        
        // 1. 优化AI模型参数
        if (shouldOptimizeModel(feedback, analysis)) {
            modelOptimizer.optimizeFromFeedback(feedback, analysis);
        }
        
        // 2. 优化提示词模板
        if (shouldOptimizePrompt(feedback, analysis)) {
            promptOptimizer.optimizeFromFeedback(feedback, analysis);
        }
        
        // 3. 优化质量规则
        if (shouldOptimizeRules(feedback, analysis)) {
            ruleOptimizer.optimizeFromFeedback(feedback, analysis);
        }
        
        // 4. 记录学习日志
        logLearningActivity(feedback, analysis, suggestions);
    }

    public void learnFromHumanReview(HumanReview review) {
        // 1. 分析人工修正
        ReviewAnalysis analysis = analyzeHumanCorrections(review);
        
        // 2. 更新抽取模式
        updateExtractionPatterns(analysis);
        
        // 3. 优化置信度计算
        optimizeConfidenceCalculation(review, analysis);
        
        // 4. 更新质量评估规则
        updateQualityRules(analysis);
    }

    private boolean shouldOptimizeModel(QualityFeedback feedback, FeedbackAnalysis analysis) {
        // 如果准确性问题较多，考虑模型优化
        return feedback.getFeedbackType() == FeedbackType.ACCURACY && 
               feedback.getFeedbackRating() <= 3;
    }
}
```

## 🎨 质量监控面板

### 实时质量指标
- **总体质量评分** - 当前批次的平均质量评分
- **准确性趋势** - 准确性评分的时间趋势
- **完整性统计** - 字段完整性统计
- **置信度分布** - 置信度评分分布图
- **人工审核率** - 需要人工审核的比例

### 质量告警
- **质量评分下降** - 质量评分低于阈值时告警
- **置信度异常** - 置信度大幅波动时告警
- **审核积压** - 人工审核任务积压时告警
- **反馈异常** - 负面反馈增多时告警

## 🧪 测试用例

### 功能测试
- 质量评估算法测试
- 人工审核流程测试
- 反馈收集和处理测试
- 学习优化效果测试
- 质量监控告警测试

### 性能测试
- 大批量质量评估测试
- 并发审核处理测试
- 学习算法性能测试
- 监控系统响应测试

### 准确性测试
- 质量评估准确性测试
- 人工审核一致性测试
- 学习效果验证测试
- 长期质量趋势测试

---

**相关文档**:
- [文档处理引擎](./document-processing-engine.md)
- [AI抽取引擎](./ai-extraction-engine.md)
- [批量处理系统](./batch-processing-system.md)
