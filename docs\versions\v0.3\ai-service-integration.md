# AI-FDB v0.3 - AI服务集成

## 版本说明

v0.3版本在v0.2 AI核心模块基础上，新增AI辅助表结构设计功能，扩展了AI服务的应用场景。

## 继承v0.2 AI服务

v0.3版本完全继承v0.2版本的AI服务能力：
- **PaddleOCR集成** - 文档OCR识别服务
- **通义千问集成** - 语义理解和字段抽取服务

详细信息请参考：
- [v0.2 PaddleOCR技术文档](../v0.2/paddleocr-implementation.md)
- [v0.2 通义千问技术文档](../v0.2/qwen-implementation.md)

## v0.3新增AI服务

### 1. 表结构生成服务

#### 服务配置

```yaml
# application.yml
ai:
  table-generation:
    enabled: true
    model: qwen-turbo
    max-tokens: 2000
    temperature: 0.7
    timeout: 30s
```

#### 核心实现

```java
@Service
public class TableGenerationService {
    
    @Autowired
    private QwenClient qwenClient;
    
    @Value("${ai.table-generation.model}")
    private String model;
    
    public TableGenerationResult generateTableStructure(
        String tableName, 
        String description, 
        String category) {
        
        String prompt = buildTableGenerationPrompt(tableName, description, category);
        
        try {
            QwenResponse response = qwenClient.generateCompletion(
                QwenRequest.builder()
                    .model(model)
                    .messages(List.of(
                        QwenMessage.builder()
                            .role("user")
                            .content(prompt)
                            .build()
                    ))
                    .maxTokens(2000)
                    .temperature(0.7)
                    .build()
            );
            
            TableStructure structure = parseTableStructure(response.getChoices().get(0).getMessage().getContent());
            
            return TableGenerationResult.builder()
                .tableName(tableName)
                .description(description)
                .fields(structure.getFields())
                .confidence(calculateConfidence(response))
                .generatedAt(LocalDateTime.now())
                .build();
                
        } catch (Exception e) {
            throw new AIServiceException("AI生成表结构失败: " + e.getMessage());
        }
    }
    
    private String buildTableGenerationPrompt(String name, String description, String category) {
        return String.format("""
            请根据以下信息生成数据表结构：

            表名：%s
            描述：%s
            类别：%s

            请生成合适的字段列表，每个字段包含：
            - fieldName: 字段名（英文，下划线命名）
            - displayName: 显示名称（中文）
            - fieldType: 字段类型（text/number/date/boolean/select等）
            - isRequired: 是否必填
            - validationRules: 验证规则
            - description: 字段说明

            请以JSON格式返回，格式如下：
            {
              "fields": [
                {
                  "fieldName": "name",
                  "displayName": "姓名",
                  "fieldType": "text",
                  "isRequired": true,
                  "validationRules": {"maxLength": 50},
                  "description": "用户姓名"
                }
              ]
            }
            """, name, description, category);
    }
}
```

### 2. 字段推荐服务

```java
@Service
public class FieldRecommendationService {
    
    @Autowired
    private QwenClient qwenClient;
    
    public List<FieldSuggestion> suggestFields(
        String tableName, 
        String tableDescription,
        List<String> existingFields) {
        
        String prompt = buildFieldSuggestionPrompt(
            tableName, tableDescription, existingFields);
        
        try {
            QwenResponse response = qwenClient.generateCompletion(
                QwenRequest.builder()
                    .model("qwen-turbo")
                    .messages(List.of(
                        QwenMessage.builder()
                            .role("user")
                            .content(prompt)
                            .build()
                    ))
                    .maxTokens(1500)
                    .temperature(0.6)
                    .build()
            );
            
            return parseFieldSuggestions(response.getChoices().get(0).getMessage().getContent());
            
        } catch (Exception e) {
            throw new AIServiceException("AI字段推荐失败: " + e.getMessage());
        }
    }
    
    private String buildFieldSuggestionPrompt(String tableName, String description, List<String> existingFields) {
        return String.format("""
            基于以下信息推荐额外的字段：

            表名：%s
            表描述：%s
            已有字段：%s

            请推荐3-5个可能有用的字段，每个字段包含：
            - fieldName: 字段名
            - displayName: 显示名称
            - fieldType: 字段类型
            - reason: 推荐理由

            以JSON格式返回。
            """, tableName, description, String.join(", ", existingFields));
    }
}
```

### 3. 提示词生成服务

```java
@Service
public class PromptGenerationService {
    
    @Autowired
    private QwenClient qwenClient;
    
    public String generateExtractionPrompt(
        String fieldName, 
        String fieldType, 
        String context,
        List<String> examples) {
        
        String prompt = buildPromptGenerationRequest(fieldName, fieldType, context, examples);
        
        try {
            QwenResponse response = qwenClient.generateCompletion(
                QwenRequest.builder()
                    .model("qwen-turbo")
                    .messages(List.of(
                        QwenMessage.builder()
                            .role("user")
                            .content(prompt)
                            .build()
                    ))
                    .maxTokens(800)
                    .temperature(0.5)
                    .build()
            );
            
            return extractPromptFromResponse(response.getChoices().get(0).getMessage().getContent());
            
        } catch (Exception e) {
            throw new AIServiceException("AI提示词生成失败: " + e.getMessage());
        }
    }
    
    private String buildPromptGenerationRequest(String fieldName, String fieldType, String context, List<String> examples) {
        StringBuilder promptBuilder = new StringBuilder();
        promptBuilder.append(String.format("""
            为字段'%s'（类型：%s）生成AI抽取提示词。

            上下文：%s

            """, fieldName, fieldType, context));
        
        if (!examples.isEmpty()) {
            promptBuilder.append("参考示例：\n");
            examples.forEach(example -> promptBuilder.append("- ").append(example).append("\n"));
        }
        
        promptBuilder.append("""
            
            请生成一个清晰、准确的提示词，帮助AI从文档中抽取该字段的值。
            提示词应该：
            1. 明确说明要抽取的内容
            2. 提供识别该字段的关键特征
            3. 说明期望的输出格式
            4. 处理可能的边界情况
            
            直接返回提示词内容，不需要额外说明。
            """);
        
        return promptBuilder.toString();
    }
}
```

## AI服务管理

### 1. AI历史记录

```java
@Entity
@Table(name = "ai_generation_history")
public class AIGenerationHistory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "table_id")
    private Long tableId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "generation_type")
    private GenerationType generationType;
    
    @Column(name = "input_prompt", columnDefinition = "TEXT")
    private String inputPrompt;
    
    @Column(name = "generated_result", columnDefinition = "JSON")
    private String generatedResult;
    
    @Column(name = "confidence_score")
    private BigDecimal confidenceScore;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "user_feedback")
    private UserFeedback userFeedback;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    public enum GenerationType {
        TABLE_STRUCTURE,
        FIELD_SUGGESTION,
        PROMPT_GENERATION
    }
    
    public enum UserFeedback {
        ACCEPTED,
        MODIFIED,
        REJECTED
    }
}
```

### 2. AI服务监控

```java
@Component
public class AIServiceMonitor {
    
    private final MeterRegistry meterRegistry;
    private final Counter aiRequestCounter;
    private final Timer aiResponseTimer;
    
    public AIServiceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.aiRequestCounter = Counter.builder("ai.requests.total")
            .description("Total AI service requests")
            .register(meterRegistry);
        this.aiResponseTimer = Timer.builder("ai.response.time")
            .description("AI service response time")
            .register(meterRegistry);
    }
    
    public void recordRequest(String serviceType) {
        aiRequestCounter.increment(Tags.of("service", serviceType));
    }
    
    public void recordResponseTime(String serviceType, Duration duration) {
        aiResponseTimer.record(duration, Tags.of("service", serviceType));
    }
}
```

### 3. AI服务配置

```java
@ConfigurationProperties(prefix = "ai")
@Data
public class AIServiceConfig {
    
    private TableGeneration tableGeneration = new TableGeneration();
    private FieldRecommendation fieldRecommendation = new FieldRecommendation();
    private PromptGeneration promptGeneration = new PromptGeneration();
    
    @Data
    public static class TableGeneration {
        private boolean enabled = true;
        private String model = "qwen-turbo";
        private int maxTokens = 2000;
        private double temperature = 0.7;
        private Duration timeout = Duration.ofSeconds(30);
    }
    
    @Data
    public static class FieldRecommendation {
        private boolean enabled = true;
        private String model = "qwen-turbo";
        private int maxTokens = 1500;
        private double temperature = 0.6;
        private Duration timeout = Duration.ofSeconds(20);
    }
    
    @Data
    public static class PromptGeneration {
        private boolean enabled = true;
        private String model = "qwen-turbo";
        private int maxTokens = 800;
        private double temperature = 0.5;
        private Duration timeout = Duration.ofSeconds(15);
    }
}
```

## 前端AI集成

### 1. AI助手组件

```vue
<template>
  <div class="ai-assistant">
    <el-card header="AI助手">
      <el-button 
        @click="suggestFields"
        :loading="suggesting"
        type="primary"
        style="width: 100%; margin-bottom: 12px"
      >
        智能推荐字段
      </el-button>
      
      <el-button 
        @click="generatePrompts"
        :loading="generating"
        style="width: 100%; margin-bottom: 12px"
      >
        生成抽取提示词
      </el-button>
      
      <el-button 
        @click="optimizeStructure"
        :loading="optimizing"
        style="width: 100%"
      >
        优化表结构
      </el-button>
    </el-card>
    
    <el-card header="AI建议" style="margin-top: 16px" v-if="suggestions.length > 0">
      <div v-for="suggestion in suggestions" :key="suggestion.id" class="suggestion-item">
        <div class="suggestion-content">
          <h4>{{ suggestion.fieldName }}</h4>
          <p>{{ suggestion.reason }}</p>
        </div>
        <div class="suggestion-actions">
          <el-button @click="applySuggestion(suggestion)" size="small" type="primary">
            应用
          </el-button>
          <el-button @click="rejectSuggestion(suggestion)" size="small">
            忽略
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref } from 'vue'
import { aiService } from '@/services/aiService'

export default {
  name: 'AIAssistant',
  props: {
    tableInfo: {
      type: Object,
      required: true
    },
    existingFields: {
      type: Array,
      default: () => []
    }
  },
  emits: ['apply-suggestions', 'generate-prompts'],
  setup(props, { emit }) {
    const suggesting = ref(false)
    const generating = ref(false)
    const optimizing = ref(false)
    const suggestions = ref([])
    
    const suggestFields = async () => {
      suggesting.value = true
      try {
        const result = await aiService.suggestFields({
          tableName: props.tableInfo.name,
          description: props.tableInfo.description,
          existingFields: props.existingFields.map(f => f.fieldName)
        })
        suggestions.value = result.suggestions
      } catch (error) {
        ElMessage.error('AI推荐失败: ' + error.message)
      } finally {
        suggesting.value = false
      }
    }
    
    const generatePrompts = async () => {
      generating.value = true
      try {
        const prompts = await aiService.generatePrompts({
          tableInfo: props.tableInfo,
          fields: props.existingFields
        })
        emit('generate-prompts', prompts)
      } catch (error) {
        ElMessage.error('提示词生成失败: ' + error.message)
      } finally {
        generating.value = false
      }
    }
    
    const applySuggestion = (suggestion) => {
      emit('apply-suggestions', [suggestion])
      suggestions.value = suggestions.value.filter(s => s.id !== suggestion.id)
    }
    
    return {
      suggesting,
      generating,
      optimizing,
      suggestions,
      suggestFields,
      generatePrompts,
      applySuggestion
    }
  }
}
</script>
```

### 2. AI服务API

```javascript
// services/aiService.js
import axios from 'axios'

class AIService {
  
  async generateTableStructure(request) {
    const response = await axios.post('/api/ai/generate-table', request)
    return response.data
  }
  
  async suggestFields(request) {
    const response = await axios.post('/api/ai/suggest-fields', request)
    return response.data
  }
  
  async generatePrompts(request) {
    const response = await axios.post('/api/ai/generate-prompts', request)
    return response.data
  }
  
  async optimizeStructure(request) {
    const response = await axios.post('/api/ai/optimize-structure', request)
    return response.data
  }
}

export const aiService = new AIService()
```

## 错误处理和重试

### 1. 重试机制

```java
@Component
public class AIServiceRetryHandler {
    
    @Retryable(
        value = {AIServiceException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public AIResponse callAIService(AIRequest request) {
        // AI服务调用逻辑
    }
    
    @Recover
    public AIResponse recover(AIServiceException ex, AIRequest request) {
        // 重试失败后的恢复逻辑
        return AIResponse.builder()
            .success(false)
            .error("AI服务暂时不可用，请稍后重试")
            .build();
    }
}
```

### 2. 降级策略

```java
@Component
public class AIServiceFallback {
    
    public TableGenerationResult fallbackTableGeneration(String tableName, String description) {
        // 使用预定义模板作为降级方案
        return templateService.getDefaultTemplate(description);
    }
    
    public List<FieldSuggestion> fallbackFieldSuggestion(String tableName) {
        // 使用规则引擎作为降级方案
        return ruleEngine.suggestFields(tableName);
    }
}
```
