# AI-FDB 项目结构文档

## 项目概述

AI-FDB（AI文件数据管理系统）是一个基于AI技术的文件数据管理系统，采用前后端分离架构，核心目标是将非结构化电子文件转换为结构化数据，并支持自然语言查询。

## 技术架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)   │    │  后端 (Spring)  │    │   数据库层      │
│                 │    │                 │    │                 │
│ - Vue 3         │◄──►│ - Spring Boot   │◄──►│ - MySQL 8.0     │
│ - Element Plus  │    │ - Spring Security│    │ - MongoDB       │
│ - Pinia         │    │ - JWT           │    │ - Redis         │
│ - Vue Router    │    │ - JPA           │    │ - MinIO         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲
                                │
                       ┌─────────────────┐
                       │   AI服务层      │
                       │                 │
                       │ - PaddleOCR     │
                       │ - 通义千问      │
                       │ - 语义抽取      │
                       └─────────────────┘
```

### 核心技术栈

#### 前端技术栈
- **框架**: Vue 3 + TypeScript + Vite
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **图表**: ECharts
- **文件上传**: vue-upload-component

#### 后端技术栈
- **框架**: Spring Boot 3.x
- **安全**: Spring Security + JWT
- **数据访问**: Spring Data JPA
- **数据库**: MySQL 8.0 + MongoDB + Redis
- **文件存储**: MinIO/本地存储
- **OCR引擎**: PaddleOCR PP-OCRv5_server_rec
- **AI模型**: 阿里通义千问 qwen-turbo

## 项目目录结构

```
AI-FDB/
├── backend/                    # 后端项目
│   ├── src/main/java/com/aifdb/
│   │   ├── config/            # 配置类
│   │   │   ├── SecurityConfig.java
│   │   │   ├── DatabaseConfig.java
│   │   │   ├── RedisConfig.java
│   │   │   ├── CorsConfig.java
│   │   │   └── AIServiceConfig.java
│   │   ├── controller/        # 控制器层
│   │   │   ├── AuthController.java
│   │   │   ├── UserController.java
│   │   │   ├── WorkspaceController.java
│   │   │   ├── DataTableController.java
│   │   │   ├── RecordController.java
│   │   │   ├── FileController.java
│   │   │   └── AIController.java
│   │   ├── service/           # 服务层
│   │   │   ├── UserService.java
│   │   │   ├── AuthService.java
│   │   │   ├── WorkspaceService.java
│   │   │   ├── DataTableService.java
│   │   │   ├── RecordService.java
│   │   │   ├── FileService.java
│   │   │   ├── OCRService.java
│   │   │   └── AIService.java
│   │   ├── repository/        # 数据访问层
│   │   │   ├── UserRepository.java
│   │   │   ├── UserSessionRepository.java
│   │   │   ├── WorkspaceRepository.java
│   │   │   ├── DataTableRepository.java
│   │   │   ├── RecordRepository.java
│   │   │   └── FileRepository.java
│   │   ├── entity/            # 实体类
│   │   │   ├── User.java
│   │   │   ├── UserSession.java
│   │   │   ├── Workspace.java
│   │   │   ├── DataTable.java
│   │   │   ├── TableField.java
│   │   │   ├── DataRecord.java
│   │   │   ├── File.java
│   │   │   └── AITask.java
│   │   ├── dto/               # 数据传输对象
│   │   │   ├── request/
│   │   │   │   ├── RegisterRequest.java
│   │   │   │   ├── LoginRequest.java
│   │   │   │   ├── CreateWorkspaceRequest.java
│   │   │   │   ├── CreateTableRequest.java
│   │   │   │   └── CreateRecordRequest.java
│   │   │   └── response/
│   │   │       ├── AuthResponse.java
│   │   │       ├── UserResponse.java
│   │   │       ├── WorkspaceResponse.java
│   │   │       └── ApiResponse.java
│   │   ├── utils/             # 工具类
│   │   │   ├── JwtUtils.java
│   │   │   ├── FileUtils.java
│   │   │   ├── PasswordUtils.java
│   │   │   ├── ValidationUtils.java
│   │   │   └── AIUtils.java
│   │   ├── exception/         # 异常处理
│   │   │   ├── GlobalExceptionHandler.java
│   │   │   ├── BusinessException.java
│   │   │   ├── AuthenticationException.java
│   │   │   └── ValidationException.java
│   │   └── security/          # 安全相关
│   │       ├── JwtAuthenticationFilter.java
│   │       ├── JwtAuthenticationEntryPoint.java
│   │       └── UserDetailsServiceImpl.java
│   ├── src/main/resources/
│   │   ├── application.yml
│   │   ├── application-dev.yml
│   │   ├── application-prod.yml
│   │   └── db/migration/      # 数据库迁移脚本
│   └── pom.xml
├── frontend/                   # 前端项目
│   ├── src/
│   │   ├── assets/            # 静态资源
│   │   │   ├── images/
│   │   │   ├── icons/
│   │   │   └── styles/
│   │   │       ├── global.css
│   │   │       ├── variables.css
│   │   │       └── components.css
│   │   ├── components/        # 公共组件
│   │   │   ├── common/        # 通用组件
│   │   │   │   ├── Header.vue
│   │   │   │   ├── Sidebar.vue
│   │   │   │   ├── Loading.vue
│   │   │   │   ├── Pagination.vue
│   │   │   │   └── ConfirmDialog.vue
│   │   │   ├── forms/         # 表单组件
│   │   │   │   ├── LoginForm.vue
│   │   │   │   ├── RegisterForm.vue
│   │   │   │   ├── TableForm.vue
│   │   │   │   ├── RecordForm.vue
│   │   │   │   └── FileUpload.vue
│   │   │   ├── charts/        # 图表组件
│   │   │   │   ├── DataChart.vue
│   │   │   │   └── StatisticsChart.vue
│   │   │   └── workspace/     # 工作空间组件
│   │   │       ├── WorkspaceCard.vue
│   │   │       ├── TableCard.vue
│   │   │       └── RecordTable.vue
│   │   ├── views/             # 页面组件
│   │   │   ├── auth/          # 认证页面
│   │   │   │   ├── Login.vue
│   │   │   │   ├── Register.vue
│   │   │   │   └── ForgotPassword.vue
│   │   │   ├── dashboard/     # 仪表板
│   │   │   │   ├── Dashboard.vue
│   │   │   │   └── Statistics.vue
│   │   │   ├── workspace/     # 工作空间
│   │   │   │   ├── WorkspaceList.vue
│   │   │   │   ├── WorkspaceDetail.vue
│   │   │   │   └── CreateWorkspace.vue
│   │   │   ├── table/         # 数据表
│   │   │   │   ├── TableList.vue
│   │   │   │   ├── TableDetail.vue
│   │   │   │   ├── CreateTable.vue
│   │   │   │   └── TableDesigner.vue
│   │   │   ├── record/        # 记录管理
│   │   │   │   ├── RecordList.vue
│   │   │   │   ├── RecordDetail.vue
│   │   │   │   ├── BatchUpload.vue
│   │   │   │   └── QueryInterface.vue
│   │   │   ├── datacenter/    # 数据中心
│   │   │   │   ├── ExampleTables.vue
│   │   │   │   └── TablePreview.vue
│   │   │   └── profile/       # 个人中心
│   │   │       ├── Profile.vue
│   │   │       └── Settings.vue
│   │   ├── stores/            # 状态管理
│   │   │   ├── auth.ts
│   │   │   ├── user.ts
│   │   │   ├── workspace.ts
│   │   │   ├── table.ts
│   │   │   ├── record.ts
│   │   │   └── app.ts
│   │   ├── router/            # 路由配置
│   │   │   ├── index.ts
│   │   │   ├── guards.ts
│   │   │   └── routes.ts
│   │   ├── api/               # API接口
│   │   │   ├── auth.ts
│   │   │   ├── user.ts
│   │   │   ├── workspace.ts
│   │   │   ├── table.ts
│   │   │   ├── record.ts
│   │   │   ├── file.ts
│   │   │   └── ai.ts
│   │   ├── utils/             # 工具函数
│   │   │   ├── request.ts
│   │   │   ├── auth.ts
│   │   │   ├── file.ts
│   │   │   ├── validation.ts
│   │   │   ├── format.ts
│   │   │   └── constants.ts
│   │   ├── types/             # TypeScript类型定义
│   │   │   ├── auth.ts
│   │   │   ├── user.ts
│   │   │   ├── workspace.ts
│   │   │   ├── table.ts
│   │   │   ├── record.ts
│   │   │   ├── file.ts
│   │   │   └── common.ts
│   │   ├── composables/       # 组合式函数
│   │   │   ├── useAuth.ts
│   │   │   ├── useTable.ts
│   │   │   ├── useFile.ts
│   │   │   └── usePermission.ts
│   │   ├── App.vue
│   │   └── main.ts
│   ├── public/
│   ├── package.json
│   ├── vite.config.ts
│   └── tsconfig.json
├── docs/                       # 项目文档
│   ├── README.md
│   ├── project_rules.md
│   ├── project-structure.md
│   ├── database-design.md
│   ├── backend-api.md
│   ├── deployment/
│   └── versions/
├── scripts/                    # 脚本文件
│   ├── build.sh
│   ├── deploy.sh
│   ├── init-db.sql
│   └── docker/
├── docker-compose.yml
├── .gitignore
└── README.md
```

## 模块依赖关系

### 前端模块依赖
```
App.vue
├── Router (路由管理)
├── Store (状态管理)
│   ├── auth (认证状态)
│   ├── workspace (工作空间状态)
│   ├── table (数据表状态)
│   └── record (记录状态)
├── Views (页面组件)
│   ├── Auth (认证页面)
│   ├── Dashboard (仪表板)
│   ├── Workspace (工作空间)
│   ├── Table (数据表)
│   └── Record (记录管理)
└── Components (公共组件)
    ├── Common (通用组件)
    ├── Forms (表单组件)
    └── Charts (图表组件)
```

### 后端模块依赖
```
Controller Layer (控制器层)
├── Service Layer (服务层)
│   ├── Repository Layer (数据访问层)
│   ├── External Services (外部服务)
│   │   ├── OCR Service (OCR服务)
│   │   └── AI Service (AI服务)
│   └── Utils (工具类)
├── Security (安全模块)
│   ├── JWT Filter (JWT过滤器)
│   └── Authentication (认证服务)
└── Exception Handler (异常处理)
```

## 数据流向

### 用户认证流程
```
前端登录 → 后端验证 → JWT生成 → Redis存储 → 前端状态更新
```

### 文件处理流程
```
文件上传 → 文件存储 → OCR识别 → AI抽取 → 数据入库 → 前端展示
```

### 查询处理流程
```
自然语言查询 → AI语义解析 → SQL生成 → 数据库查询 → 结果返回
```

## 部署架构

### 开发环境
```
本地开发机
├── 前端开发服务器 (Vite Dev Server)
├── 后端开发服务器 (Spring Boot)
├── MySQL 数据库
├── Redis 缓存
└── MongoDB 文档存储
```

### 生产环境
```
负载均衡器 (Nginx)
├── 前端静态资源服务
├── 后端API服务集群
├── 数据库集群 (MySQL主从)
├── Redis集群
├── MongoDB副本集
└── 文件存储 (MinIO集群)
```

## 扩展性设计

### 水平扩展
- 前端: CDN + 静态资源分离
- 后端: 微服务架构 + 容器化部署
- 数据库: 读写分离 + 分库分表
- 缓存: Redis集群
- 文件存储: 分布式存储

### 功能扩展
- 插件化AI模型接入
- 多租户支持
- 国际化支持
- 移动端适配
- API开放平台
