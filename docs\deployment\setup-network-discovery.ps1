# Windows局域网发现和共享设置脚本 (PowerShell版本)
# 本地IP: *************

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误：需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Windows局域网发现和共享设置脚本" -ForegroundColor Cyan
Write-Host "本地IP: *************" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 函数：显示步骤状态
function Show-Status {
    param(
        [string]$Message,
        [bool]$Success
    )
    if ($Success) {
        Write-Host "✓ $Message" -ForegroundColor Green
    } else {
        Write-Host "✗ $Message" -ForegroundColor Red
    }
}

# 1. 启用网络发现
Write-Host "[1/8] 启用网络发现..." -ForegroundColor Yellow
try {
    netsh advfirewall firewall set rule group="Network Discovery" new enable=Yes | Out-Null
    netsh advfirewall firewall set rule group="网络发现" new enable=Yes | Out-Null
    Show-Status "网络发现已启用" $true
} catch {
    Show-Status "网络发现启用失败: $($_.Exception.Message)" $false
}

# 2. 启用文件和打印机共享
Write-Host "[2/8] 启用文件和打印机共享..." -ForegroundColor Yellow
try {
    netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=Yes | Out-Null
    netsh advfirewall firewall set rule group="文件和打印机共享" new enable=Yes | Out-Null
    Show-Status "文件和打印机共享已启用" $true
} catch {
    Show-Status "文件和打印机共享启用失败: $($_.Exception.Message)" $false
}

# 3. 设置网络为专用网络
Write-Host "[3/8] 配置网络为专用网络..." -ForegroundColor Yellow
try {
    Get-NetConnectionProfile | Set-NetConnectionProfile -NetworkCategory Private
    Show-Status "网络已设置为专用" $true
} catch {
    Show-Status "网络设置失败: $($_.Exception.Message)" $false
}

# 4. 启用SMB服务
Write-Host "[4/8] 启用SMB服务..." -ForegroundColor Yellow
try {
    Set-Service -Name "lanmanserver" -StartupType Automatic
    Start-Service -Name "lanmanserver" -ErrorAction SilentlyContinue
    Show-Status "SMB服务已启动" $true
} catch {
    Show-Status "SMB服务启动失败: $($_.Exception.Message)" $false
}

# 5. 启用NetBIOS over TCP/IP
Write-Host "[5/8] 启用NetBIOS over TCP/IP..." -ForegroundColor Yellow
try {
    $adapters = Get-WmiObject -Class Win32_NetworkAdapterConfiguration | Where-Object { $_.IPEnabled -eq $true }
    foreach ($adapter in $adapters) {
        $adapter.SetTcpipNetbios(1) | Out-Null
    }
    Show-Status "NetBIOS over TCP/IP已启用" $true
} catch {
    Show-Status "NetBIOS设置失败: $($_.Exception.Message)" $false
}

# 6. 配置防火墙规则
Write-Host "[6/8] 配置防火墙规则..." -ForegroundColor Yellow
try {
    # SMB规则
    New-NetFirewallRule -DisplayName "SMB-In" -Direction Inbound -Protocol TCP -LocalPort 445 -Action Allow -ErrorAction SilentlyContinue | Out-Null
    New-NetFirewallRule -DisplayName "SMB-Out" -Direction Outbound -Protocol TCP -LocalPort 445 -Action Allow -ErrorAction SilentlyContinue | Out-Null
    
    # NetBIOS规则
    New-NetFirewallRule -DisplayName "NetBIOS-In" -Direction Inbound -Protocol TCP -LocalPort 139 -Action Allow -ErrorAction SilentlyContinue | Out-Null
    New-NetFirewallRule -DisplayName "NetBIOS-UDP-In" -Direction Inbound -Protocol UDP -LocalPort 137,138 -Action Allow -ErrorAction SilentlyContinue | Out-Null
    
    # ICMP规则
    New-NetFirewallRule -DisplayName "ICMP-In" -Direction Inbound -Protocol ICMPv4 -IcmpType 8 -Action Allow -ErrorAction SilentlyContinue | Out-Null
    
    Show-Status "防火墙规则已配置" $true
} catch {
    Show-Status "防火墙规则配置失败: $($_.Exception.Message)" $false
}

# 7. 启用相关Windows服务
Write-Host "[7/8] 启用相关Windows服务..." -ForegroundColor Yellow
try {
    $services = @("Dnscache", "FDResPub", "SSDPSRV", "upnphost")
    foreach ($service in $services) {
        Set-Service -Name $service -StartupType Automatic -ErrorAction SilentlyContinue
        Start-Service -Name $service -ErrorAction SilentlyContinue
    }
    Show-Status "相关服务已启动" $true
} catch {
    Show-Status "服务启动失败: $($_.Exception.Message)" $false
}

# 8. 验证网络设置
Write-Host "[8/8] 验证网络设置..." -ForegroundColor Yellow

# 检查网络连接
$gatewayTest = Test-NetConnection -ComputerName "*************" -Port 80 -InformationLevel Quiet -WarningAction SilentlyContinue
if ($gatewayTest) {
    Show-Status "网关连接正常" $true
} else {
    Show-Status "网关连接可能有问题" $false
}

# 获取网络信息
$networkProfile = Get-NetConnectionProfile
$ipConfig = Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -like "192.168.124.*" }

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "配置完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 显示当前网络信息
Write-Host "当前网络信息：" -ForegroundColor White
Write-Host "- 计算机名: $env:COMPUTERNAME" -ForegroundColor Gray
Write-Host "- 本地IP: *************" -ForegroundColor Gray
Write-Host "- 网络类型: $($networkProfile.NetworkCategory)" -ForegroundColor Gray
Write-Host "- 网络名称: $($networkProfile.Name)" -ForegroundColor Gray
Write-Host ""

Write-Host "访问方式：" -ForegroundColor White
Write-Host "1. 网络路径: \\$env:COMPUTERNAME" -ForegroundColor Gray
Write-Host "2. IP路径: \\*************" -ForegroundColor Gray
Write-Host ""

Write-Host "局域网内其他设备访问方法：" -ForegroundColor White
Write-Host "1. 文件资源管理器地址栏输入: \\$env:COMPUTERNAME" -ForegroundColor Gray
Write-Host "2. 文件资源管理器地址栏输入: \\*************" -ForegroundColor Gray
Write-Host "3. 运行对话框(Win+R)输入上述路径" -ForegroundColor Gray
Write-Host ""

Write-Host "共享文件夹设置：" -ForegroundColor White
Write-Host "1. 右键点击要共享的文件夹" -ForegroundColor Gray
Write-Host "2. 选择'属性' → '共享'选项卡" -ForegroundColor Gray
Write-Host "3. 点击'高级共享'并设置权限" -ForegroundColor Gray
Write-Host "4. 或使用'共享向导'进行简单设置" -ForegroundColor Gray
Write-Host ""

Write-Host "注意事项：" -ForegroundColor Yellow
Write-Host "- 确保所有设备都在同一局域网内(192.168.124.x)" -ForegroundColor Gray
Write-Host "- 如果仍无法访问，请检查目标设备的防火墙设置" -ForegroundColor Gray
Write-Host "- 建议重启计算机以确保所有设置生效" -ForegroundColor Gray
Write-Host ""

# 提供测试命令
Write-Host "测试命令：" -ForegroundColor White
Write-Host "ping *************  # 测试网络连通性" -ForegroundColor Gray
Write-Host "net view \\*************  # 查看共享资源" -ForegroundColor Gray
Write-Host ""

Read-Host "按任意键退出"