# AI-FDB v0.1 - 前端实现详细设计

## 技术架构

### 核心技术栈
- **Vue 3.4.x**: 前端框架，使用Composition API
- **TypeScript 5.x**: 类型安全的JavaScript超集
- **Vite 5.x**: 现代化构建工具
- **Element Plus 2.x**: UI组件库
- **Pinia 2.x**: 状态管理库
- **Vue Router 4.x**: 路由管理
- **Axios**: HTTP客户端
- **VueUse**: Vue组合式工具库

### 项目依赖配置

```json
{
  "name": "ai-fdb-frontend",
  "version": "0.1.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore",
    "format": "prettier --write src/"
  },
  "dependencies": {
    "vue": "^3.4.15",
    "vue-router": "^4.2.5",
    "pinia": "^2.1.7",
    "element-plus": "^2.4.4",
    "axios": "^1.6.5",
    "@vueuse/core": "^10.7.1",
    "@element-plus/icons-vue": "^2.3.1",
    "dayjs": "^1.11.10",
    "lodash-es": "^4.17.21",
    "nprogress": "^0.2.0"
  },
  "devDependencies": {
    "@types/node": "^20.11.5",
    "@types/lodash-es": "^4.17.12",
    "@types/nprogress": "^0.2.3",
    "@typescript-eslint/eslint-plugin": "^6.19.0",
    "@typescript-eslint/parser": "^6.19.0",
    "@vitejs/plugin-vue": "^5.0.3",
    "@vue/eslint-config-prettier": "^9.0.0",
    "@vue/eslint-config-typescript": "^12.0.0",
    "eslint": "^8.56.0",
    "eslint-plugin-vue": "^9.20.1",
    "prettier": "^3.2.4",
    "typescript": "~5.3.3",
    "unplugin-auto-import": "^0.17.3",
    "unplugin-vue-components": "^0.26.0",
    "vite": "^5.0.11",
    "vue-tsc": "^1.8.27"
  }
}
```

### Vite配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core'
      ],
      dts: true,
      eslintrc: {
        enabled: true
      }
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'vue-vendor': ['vue', 'vue-router', 'pinia']
        }
      }
    }
  }
})
```

## 核心类型定义

### 1. 用户相关类型

```typescript
// src/types/user.ts
export interface User {
  id: number
  username: string
  email: string
  phone?: string
  avatarUrl?: string
  status: UserStatus
  role: UserRole
  emailVerified: boolean
  phoneVerified: boolean
  lastLoginAt?: string
  loginCount: number
  createdAt: string
  updatedAt: string
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  DISABLED = 'DISABLED',
  PENDING = 'PENDING'
}

export enum UserRole {
  GUEST = 'GUEST',
  USER = 'USER',
  ADMIN = 'ADMIN'
}

export interface UpdateUserRequest {
  phone?: string
  avatarUrl?: string
}

export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}
```

### 2. 认证相关类型

```typescript
// src/types/auth.ts
export interface LoginRequest {
  usernameOrEmail: string
  password: string
  deviceInfo?: DeviceInfo
}

export interface RegisterRequest {
  username: string
  email: string
  phone?: string
  password: string
  confirmPassword: string
}

export interface AuthResponse {
  accessToken: string
  refreshToken: string
  tokenType: string
  expiresIn: string
  user: User
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface DeviceInfo {
  deviceId?: string
  deviceName?: string
  deviceType?: 'web' | 'mobile' | 'desktop' | 'tablet'
}
```

### 3. 通用类型

```typescript
// src/types/common.ts
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  code?: number
  timestamp?: string
}

export interface PaginationParams {
  page: number
  size: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface PaginationResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

export interface FormRules {
  [key: string]: Array<{
    required?: boolean
    message?: string
    trigger?: string | string[]
    min?: number
    max?: number
    pattern?: RegExp
    validator?: (rule: any, value: any, callback: any) => void
  }>
}
```

## 状态管理

### 1. 认证状态管理

```typescript
// src/stores/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginRequest, RegisterRequest, AuthResponse } from '@/types'
import { authApi } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>(getToken() || '')
  const refreshToken = ref<string>('')
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'ADMIN')
  const userDisplayName = computed(() => user.value?.username || '未知用户')

  // 登录
  const login = async (loginData: LoginRequest): Promise<void> => {
    try {
      isLoading.value = true
      const response = await authApi.login(loginData)
      
      if (response.success && response.data) {
        const authData: AuthResponse = response.data
        
        // 保存认证信息
        token.value = authData.accessToken
        refreshToken.value = authData.refreshToken
        user.value = authData.user
        
        // 持久化存储
        setToken(authData.accessToken)
        localStorage.setItem('refreshToken', authData.refreshToken)
        localStorage.setItem('user', JSON.stringify(authData.user))
        
        ElMessage.success('登录成功')
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (registerData: RegisterRequest): Promise<void> => {
    try {
      isLoading.value = true
      const response = await authApi.register(registerData)
      
      if (response.success) {
        ElMessage.success('注册成功，请登录')
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error: any) {
      ElMessage.error(error.message || '注册失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      user.value = null
      token.value = ''
      refreshToken.value = ''
      
      // 清除持久化存储
      removeToken()
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      
      ElMessage.success('已退出登录')
    }
  }

  // 刷新令牌
  const refreshAccessToken = async (): Promise<boolean> => {
    try {
      const storedRefreshToken = localStorage.getItem('refreshToken')
      if (!storedRefreshToken) {
        return false
      }

      const response = await authApi.refreshToken({ refreshToken: storedRefreshToken })
      
      if (response.success && response.data) {
        const authData: AuthResponse = response.data
        
        token.value = authData.accessToken
        user.value = authData.user
        
        setToken(authData.accessToken)
        localStorage.setItem('user', JSON.stringify(authData.user))
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('刷新令牌失败:', error)
      await logout()
      return false
    }
  }

  // 初始化用户信息
  const initializeAuth = (): void => {
    const storedToken = getToken()
    const storedUser = localStorage.getItem('user')
    const storedRefreshToken = localStorage.getItem('refreshToken')

    if (storedToken && storedUser) {
      token.value = storedToken
      user.value = JSON.parse(storedUser)
      refreshToken.value = storedRefreshToken || ''
    }
  }

  // 获取用户信息
  const fetchUserInfo = async (): Promise<void> => {
    try {
      const response = await authApi.getUserInfo()
      if (response.success && response.data) {
        user.value = response.data
        localStorage.setItem('user', JSON.stringify(response.data))
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isLoading: readonly(isLoading),
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    userDisplayName,
    
    // 方法
    login,
    register,
    logout,
    refreshAccessToken,
    initializeAuth,
    fetchUserInfo
  }
})
```

### 2. 应用状态管理

```typescript
// src/stores/app.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  
  // 主题设置
  const isDarkMode = ref(false)
  
  // 语言设置
  const locale = ref('zh-CN')
  
  // 页面加载状态
  const pageLoading = ref(false)
  
  // 系统配置
  const systemConfig = ref({
    name: 'AI-FDB',
    version: '0.1.0',
    description: 'AI文件数据管理系统'
  })

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  // 切换主题
  const toggleTheme = () => {
    isDarkMode.value = !isDarkMode.value
    // 保存到本地存储
    localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light')
  }

  // 设置语言
  const setLocale = (newLocale: string) => {
    locale.value = newLocale
    localStorage.setItem('locale', newLocale)
  }

  // 设置页面加载状态
  const setPageLoading = (loading: boolean) => {
    pageLoading.value = loading
  }

  // 初始化应用设置
  const initializeApp = () => {
    // 恢复主题设置
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      isDarkMode.value = savedTheme === 'dark'
    }

    // 恢复语言设置
    const savedLocale = localStorage.getItem('locale')
    if (savedLocale) {
      locale.value = savedLocale
    }

    // 恢复侧边栏状态
    const savedSidebar = localStorage.getItem('sidebarCollapsed')
    if (savedSidebar) {
      sidebarCollapsed.value = JSON.parse(savedSidebar)
    }
  }

  return {
    // 状态
    sidebarCollapsed: readonly(sidebarCollapsed),
    isDarkMode: readonly(isDarkMode),
    locale: readonly(locale),
    pageLoading: readonly(pageLoading),
    systemConfig: readonly(systemConfig),

    // 方法
    toggleSidebar,
    toggleTheme,
    setLocale,
    setPageLoading,
    initializeApp
  }
})
```

## API接口层

### 1. HTTP请求配置

```typescript
// src/api/request.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'
import type { ApiResponse } from '@/types'

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const authStore = useAuthStore()

    // 添加认证头
    if (authStore.token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${authStore.token}`
    }

    // 添加请求ID用于追踪
    config.headers = config.headers || {}
    config.headers['X-Request-ID'] = generateRequestId()

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response

    // 检查业务状态码
    if (data.success === false) {
      // 根据错误码处理不同情况
      if (data.code === 401) {
        handleUnauthorized()
        return Promise.reject(new Error('未授权访问'))
      } else if (data.code === 403) {
        ElMessage.error('权限不足')
        return Promise.reject(new Error('权限不足'))
      } else {
        ElMessage.error(data.message || '请求失败')
        return Promise.reject(new Error(data.message || '请求失败'))
      }
    }

    return data
  },
  async (error) => {
    console.error('响应拦截器错误:', error)

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          await handleUnauthorized()
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请稍后重试')
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }

    return Promise.reject(error)
  }
)

// 处理未授权访问
const handleUnauthorized = async () => {
  const authStore = useAuthStore()

  // 尝试刷新令牌
  const refreshSuccess = await authStore.refreshAccessToken()

  if (!refreshSuccess) {
    ElMessageBox.alert('登录已过期，请重新登录', '提示', {
      confirmButtonText: '确定',
      type: 'warning'
    }).then(() => {
      authStore.logout()
      router.push('/login')
    })
  }
}

// 生成请求ID
const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

export default service
```

### 2. 认证API

```typescript
// src/api/auth.ts
import request from './request'
import type {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  RefreshTokenRequest,
  User,
  ApiResponse
} from '@/types'

export const authApi = {
  // 用户登录
  login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    return request({
      url: '/auth/login',
      method: 'post',
      data
    })
  },

  // 用户注册
  register(data: RegisterRequest): Promise<ApiResponse<User>> {
    return request({
      url: '/auth/register',
      method: 'post',
      data
    })
  },

  // 刷新令牌
  refreshToken(data: RefreshTokenRequest): Promise<ApiResponse<AuthResponse>> {
    return request({
      url: '/auth/refresh',
      method: 'post',
      data
    })
  },

  // 用户登出
  logout(): Promise<ApiResponse<void>> {
    return request({
      url: '/auth/logout',
      method: 'post'
    })
  },

  // 登出所有设备
  logoutAllDevices(): Promise<ApiResponse<void>> {
    return request({
      url: '/auth/logout-all',
      method: 'post'
    })
  },

  // 获取当前用户信息
  getUserInfo(): Promise<ApiResponse<User>> {
    return request({
      url: '/auth/me',
      method: 'get'
    })
  }
}
```

### 3. 用户API

```typescript
// src/api/user.ts
import request from './request'
import type {
  User,
  UpdateUserRequest,
  ChangePasswordRequest,
  ApiResponse
} from '@/types'

export const userApi = {
  // 获取用户信息
  getUserInfo(userId: number): Promise<ApiResponse<User>> {
    return request({
      url: `/users/${userId}`,
      method: 'get'
    })
  },

  // 更新用户信息
  updateUserInfo(userId: number, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    return request({
      url: `/users/${userId}`,
      method: 'put',
      data
    })
  },

  // 修改密码
  changePassword(userId: number, data: ChangePasswordRequest): Promise<ApiResponse<void>> {
    return request({
      url: `/users/${userId}/password`,
      method: 'put',
      data
    })
  },

  // 上传头像
  uploadAvatar(file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData()
    formData.append('file', file)

    return request({
      url: '/users/avatar',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}
```
