# AI-FDB v0.5 - 数据库设计

## 概述

v0.5版本数据库设计在继承v0.1-v0.4版本基础上，新增了文档抽取、批量处理、质量控制等相关表结构。本文档详细描述了v0.5版本新增的数据库表设计和关系。

## 🎯 设计原则

- **继承性** - 完全兼容v0.1-v0.4版本的数据库结构
- **扩展性** - 支持未来功能的扩展和优化
- **性能优化** - 合理的索引设计和查询优化
- **数据完整性** - 完善的约束和外键关系
- **可维护性** - 清晰的命名规范和文档说明

## 📊 新增表结构

### 1. 文档抽取任务表 (extraction_jobs)
```sql
CREATE TABLE extraction_jobs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    job_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    table_id BIGINT NOT NULL COMMENT '目标数据表ID',
    job_type ENUM('single_document', 'batch_documents', 'scheduled_job') DEFAULT 'single_document',
    total_documents INT DEFAULT 0 COMMENT '文档总数',
    processed_documents INT DEFAULT 0 COMMENT '已处理文档数',
    success_documents INT DEFAULT 0 COMMENT '成功处理文档数',
    failed_documents INT DEFAULT 0 COMMENT '失败处理文档数',
    status ENUM('pending', 'running', 'paused', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    priority INT DEFAULT 5 COMMENT '优先级 1-10',
    config JSON COMMENT '任务配置',
    progress_info JSON COMMENT '进度信息',
    error_summary JSON COMMENT '错误汇总',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    estimated_completion TIMESTAMP NULL COMMENT '预计完成时间',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
) COMMENT='文档抽取任务表';
```

### 2. 文档处理记录表 (document_extractions)
```sql
CREATE TABLE document_extractions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    extraction_job_id BIGINT NOT NULL COMMENT '抽取任务ID',
    document_path VARCHAR(500) NOT NULL COMMENT '文档路径',
    document_name VARCHAR(255) NOT NULL COMMENT '文档名称',
    document_size BIGINT COMMENT '文档大小（字节）',
    document_type VARCHAR(50) COMMENT '文档类型',
    document_hash VARCHAR(64) COMMENT '文档哈希值',
    processing_result JSON COMMENT '处理结果',
    extraction_result JSON COMMENT '抽取结果',
    confidence_scores JSON COMMENT '置信度评分',
    quality_score DECIMAL(3,2) COMMENT '质量评分',
    status ENUM('pending', 'processing', 'completed', 'failed', 'skipped') DEFAULT 'pending',
    error_message TEXT COMMENT '错误信息',
    processing_time INT COMMENT '处理时间（毫秒）',
    ai_model_used VARCHAR(100) COMMENT '使用的AI模型',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    started_at TIMESTAMP NULL COMMENT '开始处理时间',
    completed_at TIMESTAMP NULL COMMENT '完成处理时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (extraction_job_id) REFERENCES extraction_jobs(id) ON DELETE CASCADE,
    INDEX idx_extraction_job_id (extraction_job_id),
    INDEX idx_status (status),
    INDEX idx_document_type (document_type),
    INDEX idx_document_hash (document_hash),
    INDEX idx_quality_score (quality_score)
) COMMENT='文档处理记录表';
```

### 3. OCR识别记录表 (ocr_recognitions)
```sql
CREATE TABLE ocr_recognitions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_extraction_id BIGINT NOT NULL COMMENT '文档抽取ID',
    ocr_provider ENUM('baidu', 'tencent', 'aliyun', 'custom') NOT NULL COMMENT 'OCR服务提供商',
    image_path VARCHAR(500) COMMENT '图像路径',
    image_type ENUM('pdf_page', 'image_file', 'screenshot') COMMENT '图像类型',
    recognition_result JSON COMMENT 'OCR识别结果',
    confidence_score DECIMAL(3,2) COMMENT '识别置信度',
    processing_time INT COMMENT '处理时间（毫秒）',
    api_cost DECIMAL(10,4) COMMENT 'API调用成本',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_extraction_id) REFERENCES document_extractions(id) ON DELETE CASCADE,
    INDEX idx_document_extraction_id (document_extraction_id),
    INDEX idx_ocr_provider (ocr_provider),
    INDEX idx_status (status),
    INDEX idx_confidence_score (confidence_score)
) COMMENT='OCR识别记录表';
```

### 4. AI抽取记录表 (ai_extractions)
```sql
CREATE TABLE ai_extractions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_extraction_id BIGINT NOT NULL COMMENT '文档抽取ID',
    ai_provider ENUM('openai', 'baidu', 'alibaba', 'custom') NOT NULL COMMENT 'AI服务提供商',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    prompt_template VARCHAR(500) COMMENT '提示词模板',
    prompt_content TEXT COMMENT '完整提示词',
    extraction_result JSON COMMENT 'AI抽取结果',
    confidence_scores JSON COMMENT '字段置信度',
    token_usage JSON COMMENT 'Token使用情况',
    api_cost DECIMAL(10,4) COMMENT 'API调用成本',
    processing_time INT COMMENT '处理时间（毫秒）',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_extraction_id) REFERENCES document_extractions(id) ON DELETE CASCADE,
    INDEX idx_document_extraction_id (document_extraction_id),
    INDEX idx_ai_provider (ai_provider),
    INDEX idx_model_name (model_name),
    INDEX idx_status (status)
) COMMENT='AI抽取记录表';
```

### 5. 质量评估表 (quality_assessments)
```sql
CREATE TABLE quality_assessments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_extraction_id BIGINT NOT NULL COMMENT '文档抽取ID',
    extraction_job_id BIGINT NOT NULL COMMENT '抽取任务ID',
    overall_score DECIMAL(3,2) COMMENT '总体质量评分 0-1',
    accuracy_score DECIMAL(3,2) COMMENT '准确性评分',
    completeness_score DECIMAL(3,2) COMMENT '完整性评分',
    consistency_score DECIMAL(3,2) COMMENT '一致性评分',
    confidence_score DECIMAL(3,2) COMMENT '置信度评分',
    field_scores JSON COMMENT '各字段质量评分',
    quality_issues JSON COMMENT '质量问题列表',
    assessment_method ENUM('automatic', 'manual', 'hybrid') DEFAULT 'automatic',
    assessment_rules JSON COMMENT '评估规则',
    assessed_by BIGINT COMMENT '评估人员ID',
    assessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_extraction_id) REFERENCES document_extractions(id) ON DELETE CASCADE,
    FOREIGN KEY (extraction_job_id) REFERENCES extraction_jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (assessed_by) REFERENCES users(id),
    INDEX idx_document_extraction_id (document_extraction_id),
    INDEX idx_extraction_job_id (extraction_job_id),
    INDEX idx_overall_score (overall_score),
    INDEX idx_assessment_method (assessment_method)
) COMMENT='质量评估表';
```

### 6. 人工审核表 (human_reviews)
```sql
CREATE TABLE human_reviews (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_extraction_id BIGINT NOT NULL COMMENT '文档抽取ID',
    quality_assessment_id BIGINT NOT NULL COMMENT '质量评估ID',
    review_type ENUM('random_sampling', 'low_confidence', 'quality_issue', 'user_request') NOT NULL,
    original_data JSON COMMENT '原始抽取数据',
    reviewed_data JSON COMMENT '审核后数据',
    review_comments TEXT COMMENT '审核意见',
    corrections JSON COMMENT '修正记录',
    review_status ENUM('pending', 'in_progress', 'completed', 'rejected') DEFAULT 'pending',
    reviewer_id BIGINT COMMENT '审核员ID',
    assigned_at TIMESTAMP NULL COMMENT '分配时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    review_time INT COMMENT '审核耗时（秒）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_extraction_id) REFERENCES document_extractions(id) ON DELETE CASCADE,
    FOREIGN KEY (quality_assessment_id) REFERENCES quality_assessments(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id),
    INDEX idx_document_extraction_id (document_extraction_id),
    INDEX idx_review_status (review_status),
    INDEX idx_reviewer_id (reviewer_id),
    INDEX idx_review_type (review_type)
) COMMENT='人工审核表';
```

### 7. 质量反馈表 (quality_feedback)
```sql
CREATE TABLE quality_feedback (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_extraction_id BIGINT NOT NULL COMMENT '文档抽取ID',
    feedback_type ENUM('accuracy', 'completeness', 'format', 'performance', 'other') NOT NULL,
    feedback_rating INT COMMENT '评分 1-5',
    feedback_content TEXT COMMENT '反馈内容',
    suggested_improvements JSON COMMENT '改进建议',
    affected_fields JSON COMMENT '影响的字段',
    user_id BIGINT NOT NULL COMMENT '反馈用户ID',
    is_processed BOOLEAN DEFAULT FALSE COMMENT '是否已处理',
    processed_by BIGINT COMMENT '处理人员ID',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    processing_notes TEXT COMMENT '处理说明',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_extraction_id) REFERENCES document_extractions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (processed_by) REFERENCES users(id),
    INDEX idx_document_extraction_id (document_extraction_id),
    INDEX idx_feedback_type (feedback_type),
    INDEX idx_feedback_rating (feedback_rating),
    INDEX idx_is_processed (is_processed),
    INDEX idx_user_id (user_id)
) COMMENT='质量反馈表';
```

### 8. 抽取模板表 (extraction_templates)
```sql
CREATE TABLE extraction_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    template_name VARCHAR(200) NOT NULL COMMENT '模板名称',
    template_type ENUM('document_type', 'industry', 'custom') DEFAULT 'custom',
    document_types JSON COMMENT '适用文档类型',
    field_mapping JSON COMMENT '字段映射配置',
    prompt_template TEXT COMMENT '提示词模板',
    extraction_rules JSON COMMENT '抽取规则',
    quality_rules JSON COMMENT '质量规则',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    success_rate DECIMAL(3,2) COMMENT '成功率',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_template_type (template_type),
    INDEX idx_is_active (is_active),
    INDEX idx_is_public (is_public),
    INDEX idx_created_by (created_by),
    INDEX idx_success_rate (success_rate)
) COMMENT='抽取模板表';
```

## 🔗 表关系图

```
users (用户表)
  ├── extraction_jobs (抽取任务)
  ├── human_reviews (人工审核)
  ├── quality_feedback (质量反馈)
  └── extraction_templates (抽取模板)

data_tables (数据表)
  └── extraction_jobs (抽取任务)

extraction_jobs (抽取任务)
  ├── document_extractions (文档抽取)
  └── quality_assessments (质量评估)

document_extractions (文档抽取)
  ├── ocr_recognitions (OCR识别)
  ├── ai_extractions (AI抽取)
  ├── quality_assessments (质量评估)
  ├── human_reviews (人工审核)
  └── quality_feedback (质量反馈)

quality_assessments (质量评估)
  └── human_reviews (人工审核)
```

## 📈 性能优化

### 索引策略
- **主键索引** - 所有表都有自增主键
- **外键索引** - 所有外键字段都有索引
- **状态索引** - 状态字段的复合索引
- **时间索引** - 创建时间和更新时间索引
- **业务索引** - 根据查询模式创建的业务索引

### 分区策略
```sql
-- 按月分区大表
ALTER TABLE document_extractions 
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 查询优化
- **避免全表扫描** - 合理使用索引
- **分页查询优化** - 使用游标分页
- **JSON字段优化** - 使用虚拟列和函数索引
- **统计查询优化** - 使用汇总表和缓存

## 🔒 数据安全

### 敏感数据保护
- **数据加密** - 敏感字段使用AES加密
- **访问控制** - 基于角色的数据访问控制
- **审计日志** - 完整的数据操作日志
- **数据脱敏** - 测试环境数据脱敏

### 备份策略
- **全量备份** - 每日全量备份
- **增量备份** - 每小时增量备份
- **异地备份** - 跨地域备份存储
- **恢复测试** - 定期恢复测试

## 🧪 数据迁移

### v0.4到v0.5迁移脚本
```sql
-- 创建新表
SOURCE create_v0.5_tables.sql;

-- 数据迁移
INSERT INTO extraction_jobs (job_name, table_id, job_type, created_by, created_at)
SELECT CONCAT('Migration_', id), table_id, 'batch_documents', created_by, created_at
FROM import_jobs WHERE status = 'completed';

-- 更新索引
ANALYZE TABLE extraction_jobs, document_extractions, quality_assessments;
```

---

**相关文档**:
- [v0.1数据库设计](../v0.1/database-design.md)
- [v0.2数据库设计](../v0.2/database-design.md)
- [v0.3数据库设计](../v0.3/database-design.md)
- [v0.4数据管理实现](../v0.4/data-management-implementation.md)
