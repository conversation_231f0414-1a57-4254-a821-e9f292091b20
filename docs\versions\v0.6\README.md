# AI-FDB v0.6 - AI数据问答与可视化

## 版本概述

v0.6版本在v0.5数据记录录入与语义抽取基础上，实现AI数据问答与可视化功能。集成Vanna AI开源框架、qwen-turbo模型和Bytebase DBHub MCP服务器，用户可以通过自然语言查询工作空间中的数据表，系统自动生成SQL语句并通过安全的MCP协议执行查询，同时提供智能的数据可视化展示。本版本将AI-FDB从数据录入工具升级为智能数据分析平台。

## 🎯 可视化验证目标

完成v0.6版本后，用户可以：
1. **自然语言查询** - 使用自然语言提问，如"显示最近一个月的注册用户数量"
2. **智能表识别** - AI Agent自动识别相关的数据表和字段
3. **SQL自动生成** - 基于问题和表结构自动生成准确的SQL查询语句
4. **查询结果展示** - 以表格形式展示查询结果
5. **智能可视化** - 根据数据类型自动选择合适的图表类型（柱状图、折线图、饼图等）
6. **交互式图表** - 支持图表的缩放、筛选、导出等交互操作
7. **查询历史** - 保存和管理历史查询记录
8. **查询优化建议** - 提供SQL优化建议和性能提示

## 📋 完整实现清单

### Vanna AI + DBHub MCP集成
- [x] Vanna AI开源框架集成和配置
- [x] qwen-turbo模型适配器开发
- [x] RAG向量数据库配置（ChromaDB）
- [x] Bytebase DBHub MCP服务器集成
- [x] 支持MySQL、PostgreSQL、SQL Server、MariaDB
- [x] 自然语言到SQL转换引擎

### AI Agent + MCP 混合查询引擎
- [x] AI Agent智能理解层
  - [x] 自然语言理解模块（qwen-turbo）
  - [x] 意图识别和实体抽取
  - [x] 上下文对话管理
  - [x] 复杂查询推理
- [x] MCP数据库执行层
  - [x] 安全SQL执行（防注入）
  - [x] 数据库连接池管理
  - [x] 查询结果缓存
  - [x] 性能监控和优化
- [x] 智能路由分发
  - [x] 查询复杂度评估
  - [x] 执行路径选择
  - [x] 结果合并处理

### 数据可视化模块
- [x] 图表类型智能推荐
- [x] 交互式图表组件（基于ECharts/Chart.js）
- [x] 数据导出功能（Excel、CSV、PDF）
- [x] 图表配置和自定义
- [x] 响应式图表设计

### 训练数据管理
- [x] 数据表结构自动学习
- [x] 业务术语词典管理
- [x] 查询样本收集和标注
- [x] 模型训练数据更新
- [x] 查询质量反馈机制

### 数据库扩展
- [x] AI查询记录表
- [x] 可视化配置表
- [x] 训练数据表
- [x] 查询性能统计表
- [x] 用户反馈表

## 🚀 实施步骤

### 步骤1: 环境配置
```bash
# 1. 安装Vanna AI框架（使用国内镜像）
pip install vanna -i https://pypi.tuna.tsinghua.edu.cn/simple/
pip install chromadb -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 2. 安装DBHub MCP服务器
npm install -g @bytebase/dbhub

# 3. 安装数据可视化依赖
pip install plotly -i https://pypi.tuna.tsinghua.edu.cn/simple/
pip install pandas -i https://pypi.tuna.tsinghua.edu.cn/simple/
pip install matplotlib -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 4. 配置qwen-turbo API
export QWEN_API_KEY="sk-beff2b8bc208457a9d971610488661f0"
export QWEN_BASE_URL="https://dashscope.aliyuncs.com"

# 5. 配置数据库连接（示例）
export DSN="mysql://user:password@localhost:3306/ai_fdb?sslmode=disable"
```

### 步骤2: 数据库扩展
```sql
-- AI查询记录表
CREATE TABLE ai_queries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    user_question TEXT NOT NULL COMMENT '用户的自然语言问题',
    generated_sql TEXT COMMENT '生成的SQL语句',
    query_result JSON COMMENT '查询结果数据',
    execution_time INT COMMENT 'SQL执行时间(毫秒)',
    result_count INT COMMENT '结果记录数',
    visualization_config JSON COMMENT '可视化配置',
    user_feedback ENUM('positive', 'negative', 'neutral') COMMENT '用户反馈',
    feedback_comment TEXT COMMENT '反馈说明',
    status ENUM('pending', 'success', 'failed') DEFAULT 'pending',
    error_message TEXT,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_user (workspace_id, created_by),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
);

-- 可视化配置表
CREATE TABLE visualization_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    query_id BIGINT,
    chart_type ENUM('table', 'bar', 'line', 'pie', 'scatter', 'area', 'heatmap') NOT NULL,
    chart_config JSON NOT NULL COMMENT '图表配置参数',
    data_mapping JSON COMMENT '数据字段映射',
    style_config JSON COMMENT '样式配置',
    is_default BOOLEAN DEFAULT FALSE,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (query_id) REFERENCES ai_queries(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_query_id (query_id),
    INDEX idx_chart_type (chart_type)
);

-- 训练数据表
CREATE TABLE vanna_training_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    data_type ENUM('ddl', 'documentation', 'sql', 'question_sql') NOT NULL,
    content TEXT NOT NULL COMMENT '训练数据内容',
    metadata JSON COMMENT '元数据信息',
    quality_score DECIMAL(3,2) COMMENT '数据质量评分',
    is_active BOOLEAN DEFAULT TRUE,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_type (workspace_id, data_type),
    INDEX idx_active (is_active)
);

-- 查询性能统计表
CREATE TABLE query_performance_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    date DATE NOT NULL,
    total_queries INT DEFAULT 0,
    successful_queries INT DEFAULT 0,
    failed_queries INT DEFAULT 0,
    avg_execution_time DECIMAL(8,2) COMMENT '平均执行时间(毫秒)',
    avg_result_count DECIMAL(10,2) COMMENT '平均结果记录数',
    most_common_chart_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    UNIQUE KEY uk_workspace_date (workspace_id, date),
    INDEX idx_date (date)
);

-- 插入默认可视化配置
INSERT INTO visualization_configs (query_id, chart_type, chart_config, data_mapping, is_default, created_by) VALUES
(NULL, 'table', '{"pagination": true, "sortable": true, "searchable": true}', '{}', TRUE, 1),
(NULL, 'bar', '{"responsive": true, "legend": {"display": true}, "scales": {"y": {"beginAtZero": true}}}', '{}', TRUE, 1),
(NULL, 'line', '{"responsive": true, "legend": {"display": true}, "scales": {"y": {"beginAtZero": false}}}', '{}', TRUE, 1),
(NULL, 'pie', '{"responsive": true, "legend": {"position": "right"}}', '{}', TRUE, 1);
```

### 步骤3: DBHub MCP服务器配置
```bash
# 启动DBHub MCP服务器（HTTP模式）
npx @bytebase/dbhub \
  --transport http \
  --port 8080 \
  --dsn "mysql://user:password@localhost:3306/ai_fdb?sslmode=disable" \
  --readonly

# 或使用Docker启动
docker run --rm --init \
  --name dbhub \
  --publish 8080:8080 \
  bytebase/dbhub \
  --transport http \
  --port 8080 \
  --dsn "mysql://user:<EMAIL>:3306/ai_fdb?sslmode=disable" \
  --readonly
```

### 步骤4: 应用配置文件
```yaml
# application.yml 添加AI查询和MCP配置
ai:
  vanna:
    # Vanna AI配置
    model: qwen-turbo
    api-key: sk-beff2b8bc208457a9d971610488661f0
    base-url: https://dashscope.aliyuncs.com
    temperature: 0.1
    max-tokens: 4000
    
    # 向量数据库配置
    vector-store:
      type: chromadb
      path: ./data/vanna_vectordb
      collection-name: ai-fdb-training
    
    # 查询配置
    query:
      max-results: 1000
      timeout: 30000
      enable-cache: true
      cache-ttl: 3600
    
    # 安全配置
    security:
      allowed-operations: ["SELECT"]
      forbidden-keywords: ["DROP", "DELETE", "UPDATE", "INSERT", "ALTER", "CREATE", "TRUNCATE"]
      max-query-complexity: 10

# DBHub MCP配置
mcp:
  dbhub:
    # MCP服务器连接
    base-url: http://localhost:8080
    transport: http
    timeout: 30000

    # 支持的数据库
    databases:
      - type: mysql
        dsn: "mysql://user:password@localhost:3306/ai_fdb?sslmode=disable"
        readonly: true
      - type: postgresql
        dsn: "postgres://user:password@localhost:5432/ai_fdb?sslmode=disable"
        readonly: true

    # 安全配置
    security:
      allowed-operations: ["SELECT", "SHOW", "DESCRIBE", "EXPLAIN"]
      forbidden-keywords: ["DROP", "DELETE", "UPDATE", "INSERT", "ALTER", "CREATE", "TRUNCATE"]
      max-query-complexity: 10
      enable-sql-validation: true

visualization:
  # 图表配置
  charts:
    default-type: table
    max-data-points: 10000
    enable-export: true
    supported-formats: ["png", "pdf", "svg", "excel", "csv"]
  
  # 智能推荐配置
  recommendation:
    enable-auto-chart: true
    confidence-threshold: 0.8
    max-recommendations: 3

# 训练数据配置
training:
  auto-learn:
    enable: true
    min-feedback-score: 4
    max-training-samples: 10000
  
  data-sources:
    - type: database-schema
      auto-sync: true
      sync-interval: 24h
    - type: user-queries
      min-success-rate: 0.8
```

## 🧪 可视化验证指南

### 验证步骤1: DBHub MCP环境测试
1. **DBHub MCP服务器测试**
   - 启动DBHub MCP服务器: `npx @bytebase/dbhub --transport http --port 8080`
   - 访问 `http://localhost:8080/message` 验证MCP服务正常
   - 测试数据库连接和只读模式

2. **Vanna AI框架测试**
   - 访问 `http://localhost:3000/ai-query-test`
   - 测试qwen-turbo模型连接状态
   - 验证ChromaDB向量数据库正常工作

3. **训练数据加载测试**
   - 通过DBHub获取数据库表结构进行训练
   - 添加业务文档和术语
   - 验证训练数据存储和检索

### 验证步骤2: 自然语言查询测试
1. **基础查询测试**
   - 输入："显示所有用户"
   - 验证生成的SQL: `SELECT * FROM users`
   - 检查查询结果正确性

2. **复杂查询测试**
   - 输入："统计最近30天每天的注册用户数量"
   - 验证生成包含日期函数和GROUP BY的SQL
   - 检查时间范围和聚合逻辑

3. **多表关联查询测试**
   - 输入："显示每个工作空间的数据表数量"
   - 验证生成JOIN查询语句
   - 检查关联逻辑正确性

### 验证步骤3: 数据可视化测试
1. **图表类型自动推荐**
   - 数值统计数据 → 自动推荐柱状图
   - 时间序列数据 → 自动推荐折线图
   - 分类占比数据 → 自动推荐饼图

2. **交互式图表功能**
   - 图表缩放和平移
   - 数据点悬停显示详情
   - 图例点击切换显示

3. **数据导出功能**
   - 导出Excel格式数据
   - 导出图表为PNG/PDF
   - 验证导出数据完整性

### 验证步骤4: AI学习和优化测试
1. **用户反馈学习**
   - 对查询结果进行正面/负面反馈
   - 验证反馈数据存储
   - 检查后续相似查询的改进

2. **查询历史管理**
   - 查看历史查询记录
   - 重新执行历史查询
   - 验证查询性能统计

## ✅ 验收标准

### 功能验收
- [x] DBHub MCP服务器集成正常，支持MySQL、PostgreSQL等数据库
- [x] Vanna AI框架集成正常，支持qwen-turbo模型
- [x] 自然语言到SQL转换准确率大于85%
- [x] 支持复杂查询（多表关联、聚合函数、时间范围等）
- [x] MCP协议安全执行SQL查询，只读模式有效
- [x] 智能图表推荐功能正常工作
- [x] 支持多种图表类型和交互功能
- [x] 数据导出功能完整可用
- [x] 查询安全性验证有效

### 性能验收
- [x] 自然语言理解响应时间小于3秒
- [x] SQL生成时间小于5秒
- [x] 数据查询执行时间小于10秒（1000条记录内）
- [x] 图表渲染时间小于2秒
- [x] 支持并发查询（至少10个用户同时使用）

### 技术验收
- [x] 所有AI查询API接口测试通过
- [x] DBHub MCP服务器稳定可靠，支持HTTP和stdio传输
- [x] Vanna AI服务稳定可靠
- [x] qwen-turbo模型集成稳定
- [x] 向量数据库性能良好
- [x] MCP协议通信正常，错误处理完善
- [x] 前端组件响应式设计
- [x] 完整的错误处理和用户提示
- [x] 查询日志和性能监控完善

### 安全验收
- [x] SQL注入防护有效
- [x] 危险操作（DROP、DELETE等）被阻止
- [x] 用户权限验证正常
- [x] 数据访问控制有效
- [x] 查询复杂度限制正常

## 🔄 与前版本的集成

### v0.5版本数据利用
- **OCR识别数据** - 可以查询OCR识别的文档内容和结果
- **语义抽取数据** - 可以分析抽取的结构化字段数据
- **文档处理记录** - 可以统计文档处理的成功率和性能

### v0.4版本表结构利用
- **数据表定义** - 自动学习用户创建的数据表结构
- **字段配置** - 理解字段的业务含义和数据类型
- **表关系** - 识别表之间的关联关系

### v0.3版本工作空间集成
- **多工作空间支持** - 每个工作空间独立的AI训练模型
- **权限继承** - 基于工作空间权限控制查询范围
- **协作功能** - 团队成员共享查询历史和模板

## 🚀 后续扩展方向

### v0.7版本预期功能
- **高级分析功能** - 趋势分析、异常检测、预测分析
- **自定义仪表板** - 用户可创建个性化的数据看板
- **API接口开放** - 提供REST API供第三方系统集成
- **移动端适配** - 支持手机和平板设备的查询功能

### 技术优化方向
- **模型微调** - 基于用户数据对qwen-turbo进行领域微调
- **缓存优化** - 智能查询结果缓存和预计算
- **分布式部署** - 支持多节点部署和负载均衡
- **实时数据** - 支持实时数据流的查询和监控

## 📚 相关文档

- [DBHub MCP服务器](https://github.com/bytebase/dbhub) - Bytebase开源的通用数据库MCP服务器
- [Model Context Protocol](https://modelcontextprotocol.io/) - MCP协议官方文档
- [Vanna AI官方文档](https://vanna.ai/docs/) - 自然语言转SQL框架
- [qwen-turbo API文档](https://bailian.console.aliyun.com/) - 阿里通义千问API
- [ChromaDB使用指南](https://docs.trychroma.com/) - 向量数据库
- [ECharts图表库文档](https://echarts.apache.org/zh/index.html) - 数据可视化

---

**注意事项：**
1. 首次使用需要对工作空间进行AI训练，建议准备充分的示例数据
2. 复杂查询可能需要多次迭代优化，建议收集用户反馈持续改进
3. 大数据量查询建议设置合理的分页和限制，避免性能问题
4. 定期备份训练数据和查询历史，确保AI模型的持续优化
