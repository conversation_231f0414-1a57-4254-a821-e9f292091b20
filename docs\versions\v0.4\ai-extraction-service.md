# AI-FDB v0.4 - AI数据抽取服务

## 概述

AI数据抽取服务是v0.4版本的智能化核心功能，集成通义千问AI模型，能够从非结构化文本中智能抽取结构化数据，自动识别字段类型和内容，大幅提升数据录入效率。

## 🎯 功能目标

- **智能抽取** - 从文本中自动抽取结构化数据
- **格式识别** - 智能识别文件格式、编码和数据结构
- **字段推断** - 自动推断字段类型、含义和验证规则
- **数据清洗** - AI辅助数据标准化和质量提升
- **异常检测** - 智能检测异常数据和潜在错误
- **模板学习** - 从历史数据中学习抽取模式

## 🏗️ 系统架构

### AI抽取架构
```
文本输入 → 预处理 → AI分析 → 结果解析 → 数据验证 → 结果输出
    ↓        ↓       ↓        ↓        ↓        ↓
  文本清理  格式识别  通义千问   结构化解析  质量检查  置信度评估
```

### 核心组件
1. **TextPreprocessor** - 文本预处理器
2. **AIExtractionEngine** - AI抽取引擎
3. **QwenIntegrationService** - 通义千问集成服务
4. **ResultParser** - 结果解析器
5. **ConfidenceEvaluator** - 置信度评估器
6. **ExtractionTemplateManager** - 抽取模板管理器

## 🔧 技术实现

### AI服务配置
```yaml
# application.yml
ai:
  qwen:
    api-key: ${QWEN_API_KEY:sk-beff2b8bc208457a9d971610488661f0}
    base-url: https://dashscope.aliyuncs.com/api/v1
    model: qwen-turbo
    timeout: 30s
    max-tokens: 2000
    temperature: 0.1
    
  extraction:
    enabled: true
    confidence-threshold: 0.7
    max-retry-attempts: 3
    batch-size: 10
    
  cache:
    enabled: true
    ttl: 3600s
    max-size: 1000
```

### AI抽取引擎实现
```java
@Service
public class AIExtractionEngine {

    @Autowired
    private QwenIntegrationService qwenService;
    
    @Autowired
    private ExtractionTemplateManager templateManager;
    
    @Autowired
    private ConfidenceEvaluator confidenceEvaluator;

    public ExtractionResult extractData(Long tableId, AIExtractionRequest request) {
        DataTable table = getTableById(tableId);
        
        // 1. 文本预处理
        String processedText = preprocessText(request.getSourceContent());
        
        // 2. 构建AI提示词
        String prompt = buildExtractionPrompt(table, processedText, request.getMode());
        
        // 3. 调用AI服务
        AIResponse aiResponse = qwenService.chat(prompt);
        
        // 4. 解析AI响应
        Map<String, Object> extractedData = parseAIResponse(aiResponse.getContent());
        
        // 5. 数据验证和清洗
        Map<String, Object> cleanedData = cleanAndValidateData(table, extractedData);
        
        // 6. 计算置信度
        Map<String, Double> confidenceScores = confidenceEvaluator.evaluate(
            table, processedText, cleanedData);
        
        // 7. 构建结果
        return ExtractionResult.builder()
            .extractedData(cleanedData)
            .confidenceScores(confidenceScores)
            .sourceText(processedText)
            .aiModel(qwenService.getModelName())
            .processingTime(System.currentTimeMillis() - startTime)
            .build();
    }

    private String buildExtractionPrompt(DataTable table, String text, ExtractionMode mode) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("请从以下文本中抽取结构化数据：\n\n");
        prompt.append("文本内容：\n").append(text).append("\n\n");
        
        prompt.append("目标数据结构：\n");
        for (TableField field : table.getFields()) {
            prompt.append("- ").append(field.getDisplayName())
                  .append("（").append(field.getFieldName()).append("）")
                  .append("：").append(field.getFieldType())
                  .append(field.getRequired() ? "，必填" : "，可选");
            
            if (StringUtils.hasText(field.getDescription())) {
                prompt.append("，").append(field.getDescription());
            }
            prompt.append("\n");
        }
        
        prompt.append("\n请以JSON格式返回抽取结果，格式如下：\n");
        prompt.append("{\n");
        for (TableField field : table.getFields()) {
            prompt.append("  \"").append(field.getFieldName()).append("\": \"抽取的值\",\n");
        }
        prompt.append("}\n\n");
        
        prompt.append("注意事项：\n");
        prompt.append("1. 如果某个字段在文本中找不到对应信息，请设置为null\n");
        prompt.append("2. 请确保数据类型与字段定义匹配\n");
        prompt.append("3. 日期格式请使用YYYY-MM-DD\n");
        prompt.append("4. 数字请使用标准格式，不要包含单位\n");
        
        return prompt.toString();
    }

    private Map<String, Object> parseAIResponse(String aiContent) {
        try {
            // 提取JSON部分
            String jsonStr = extractJsonFromResponse(aiContent);
            
            // 解析JSON
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(jsonStr, Map.class);
            
        } catch (Exception e) {
            log.error("解析AI响应失败", e);
            throw new AIExtractionException("AI响应解析失败: " + e.getMessage());
        }
    }

    private Map<String, Object> cleanAndValidateData(DataTable table, Map<String, Object> rawData) {
        Map<String, Object> cleanedData = new HashMap<>();
        
        for (TableField field : table.getFields()) {
            String fieldName = field.getFieldName();
            Object rawValue = rawData.get(fieldName);
            
            if (rawValue == null) {
                if (field.getRequired()) {
                    log.warn("必填字段 {} 未找到值", fieldName);
                }
                continue;
            }
            
            // 根据字段类型进行数据清洗和转换
            Object cleanedValue = cleanFieldValue(field, rawValue);
            if (cleanedValue != null) {
                cleanedData.put(fieldName, cleanedValue);
            }
        }
        
        return cleanedData;
    }

    private Object cleanFieldValue(TableField field, Object rawValue) {
        String strValue = rawValue.toString().trim();
        
        switch (field.getFieldType()) {
            case "text":
                return strValue;
                
            case "number":
                return parseNumber(strValue);
                
            case "date":
                return parseDate(strValue);
                
            case "email":
                return validateEmail(strValue) ? strValue : null;
                
            case "phone":
                return cleanPhoneNumber(strValue);
                
            case "boolean":
                return parseBoolean(strValue);
                
            default:
                return strValue;
        }
    }
}
```

### 通义千问集成服务
```java
@Service
public class QwenIntegrationService {

    @Value("${ai.qwen.api-key}")
    private String apiKey;
    
    @Value("${ai.qwen.base-url}")
    private String baseUrl;
    
    @Value("${ai.qwen.model}")
    private String model;

    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public AIResponse chat(String prompt) {
        // 检查缓存
        String cacheKey = "ai_extraction:" + DigestUtils.md5DigestAsHex(prompt.getBytes());
        AIResponse cachedResponse = (AIResponse) redisTemplate.opsForValue().get(cacheKey);
        if (cachedResponse != null) {
            return cachedResponse;
        }
        
        // 构建请求
        QwenChatRequest request = QwenChatRequest.builder()
            .model(model)
            .messages(List.of(
                QwenMessage.builder()
                    .role("user")
                    .content(prompt)
                    .build()
            ))
            .temperature(0.1)
            .maxTokens(2000)
            .build();
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);
        
        HttpEntity<QwenChatRequest> entity = new HttpEntity<>(request, headers);
        
        try {
            // 发送请求
            ResponseEntity<QwenChatResponse> response = restTemplate.postForEntity(
                baseUrl + "/chat/completions", entity, QwenChatResponse.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                QwenChatResponse chatResponse = response.getBody();
                String content = chatResponse.getChoices().get(0).getMessage().getContent();
                
                AIResponse aiResponse = AIResponse.builder()
                    .content(content)
                    .model(model)
                    .usage(chatResponse.getUsage())
                    .timestamp(System.currentTimeMillis())
                    .build();
                
                // 缓存结果
                redisTemplate.opsForValue().set(cacheKey, aiResponse, Duration.ofHours(1));
                
                return aiResponse;
            } else {
                throw new AIServiceException("AI服务调用失败: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("调用通义千问API失败", e);
            throw new AIServiceException("AI服务调用失败: " + e.getMessage());
        }
    }
}
```

## 🎨 前端实现

### AI抽取面板组件
```vue
<template>
  <div class="ai-extraction-panel">
    <el-card header="AI数据抽取">
      <el-form :model="extractionForm" label-width="100px">
        <el-form-item label="源文本">
          <el-input 
            v-model="extractionForm.sourceContent"
            type="textarea"
            :rows="8"
            placeholder="请输入要抽取数据的文本内容，支持多行文本"
            show-word-limit
            maxlength="5000"
          />
        </el-form-item>
        
        <el-form-item label="抽取模式">
          <el-radio-group v-model="extractionForm.mode">
            <el-radio label="auto">自动抽取</el-radio>
            <el-radio label="guided">引导抽取</el-radio>
            <el-radio label="template">模板抽取</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="抽取模板" v-if="extractionForm.mode === 'template'">
          <el-select v-model="extractionForm.templateId" placeholder="选择抽取模板">
            <el-option
              v-for="template in extractionTemplates"
              :key="template.id"
              :label="template.name"
              :value="template.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="extractData"
            :loading="extracting"
            :disabled="!extractionForm.sourceContent.trim()"
          >
            <el-icon><MagicStick /></el-icon>
            开始抽取
          </el-button>
          <el-button @click="clearResults">清空结果</el-button>
          <el-button @click="showExamples = true">查看示例</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 抽取结果 -->
    <el-card header="抽取结果" v-if="extractionResult" class="result-card">
      <div class="extraction-results">
        <div 
          v-for="(value, field) in extractionResult.extractedData" 
          :key="field"
          class="result-item"
        >
          <div class="field-info">
            <span class="field-name">{{ getFieldDisplayName(field) }}</span>
            <el-tag 
              :type="getConfidenceType(extractionResult.confidenceScores[field])"
              size="small"
            >
              置信度: {{ (extractionResult.confidenceScores[field] * 100).toFixed(1) }}%
            </el-tag>
          </div>
          <div class="field-value">
            <el-input 
              v-model="extractionResult.extractedData[field]"
              @change="updateExtractedValue(field, $event)"
              :placeholder="getFieldPlaceholder(field)"
            />
          </div>
        </div>
      </div>
      
      <div class="result-actions">
        <el-button type="success" @click="acceptAllResults">
          <el-icon><Check /></el-icon>
          接受所有结果
        </el-button>
        <el-button @click="saveAsTemplate">
          <el-icon><Document /></el-icon>
          保存为模板
        </el-button>
        <el-button @click="reExtract">
          <el-icon><Refresh /></el-icon>
          重新抽取
        </el-button>
      </div>
      
      <!-- 处理信息 -->
      <div class="processing-info">
        <el-descriptions :column="2" size="small">
          <el-descriptions-item label="AI模型">
            {{ extractionResult.aiModel }}
          </el-descriptions-item>
          <el-descriptions-item label="处理时间">
            {{ extractionResult.processingTime }}ms
          </el-descriptions-item>
          <el-descriptions-item label="抽取字段">
            {{ Object.keys(extractionResult.extractedData).length }}
          </el-descriptions-item>
          <el-descriptions-item label="平均置信度">
            {{ getAverageConfidence() }}%
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { MagicStick, Check, Document, Refresh } from '@element-plus/icons-vue'

export default {
  name: 'AIExtractionPanel',
  components: {
    MagicStick,
    Check,
    Document,
    Refresh
  },
  props: {
    table: {
      type: Object,
      required: true
    }
  },
  emits: ['extracted'],
  setup(props, { emit }) {
    const extracting = ref(false)
    const extractionResult = ref(null)
    const extractionTemplates = ref([])
    const showExamples = ref(false)
    
    const extractionForm = reactive({
      sourceContent: '',
      mode: 'auto',
      templateId: null
    })

    const extractData = async () => {
      if (!extractionForm.sourceContent.trim()) {
        ElMessage.warning('请输入要抽取的文本内容')
        return
      }
      
      extracting.value = true
      try {
        const response = await api.post(`/tables/${props.table.id}/records/ai-extract`, {
          sourceContent: extractionForm.sourceContent,
          mode: extractionForm.mode,
          templateId: extractionForm.templateId
        })
        
        extractionResult.value = response.data
        ElMessage.success('数据抽取完成')
        
      } catch (error) {
        ElMessage.error('AI抽取失败: ' + (error.response?.data?.message || error.message))
      } finally {
        extracting.value = false
      }
    }

    const getConfidenceType = (confidence) => {
      if (confidence >= 0.8) return 'success'
      if (confidence >= 0.6) return 'warning'
      return 'danger'
    }

    const getAverageConfidence = () => {
      if (!extractionResult.value?.confidenceScores) return 0
      const scores = Object.values(extractionResult.value.confidenceScores)
      const average = scores.reduce((sum, score) => sum + score, 0) / scores.length
      return (average * 100).toFixed(1)
    }

    const acceptAllResults = () => {
      emit('extracted', extractionResult.value.extractedData)
      ElMessage.success('已接受抽取结果')
    }

    return {
      extracting,
      extractionResult,
      extractionForm,
      extractionTemplates,
      showExamples,
      extractData,
      getConfidenceType,
      getAverageConfidence,
      acceptAllResults
    }
  }
}
</script>
```

## 🧪 测试用例

### 功能测试
- 文本抽取准确性测试
- 不同格式文本处理测试
- 字段类型识别测试
- 置信度评估测试
- 模板学习功能测试

### 性能测试
- AI服务响应时间测试
- 大文本处理性能测试
- 并发抽取压力测试
- 缓存机制效果测试

### 准确性测试
- 抽取准确率统计
- 不同领域文本测试
- 复杂格式文本测试
- 边界情况处理测试

---

**相关文档**:
- [数据导入设计](./data-import-design.md)
- [数据管理实现](./data-management-implementation.md)
- [通义千问技术文档](../v0.2/qwen-implementation.md)
