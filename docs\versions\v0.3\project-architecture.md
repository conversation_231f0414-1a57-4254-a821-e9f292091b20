# AI-FDB v0.3 - 项目架构设计

## 版本说明

v0.3版本在v0.2 AI核心模块基础上，新增工作空间管理和数据表设计功能，保持架构的连续性和一致性。

## 继承v0.2架构组件

v0.3版本完全继承v0.2版本的架构设计：

### 基础架构层
- **用户认证系统** - JWT认证、权限管理、会话管理
- **OCR处理引擎** - PaddleOCR PP-OCRv5_server_rec集成
- **AI语义理解** - 通义千问qwen-turbo模型集成
- **数据存储层** - MySQL + Redis架构

### 技术栈
- **后端**: Java 17 + Spring Boot 3.2 + Spring Security
- **前端**: Vue 3 + TypeScript + Element Plus + Vite
- **数据库**: MySQL 8.0 + Redis
- **AI服务**: PaddleOCR + 通义千问qwen-turbo

详细信息请参考：
- [v0.1项目架构](../v0.1/project-structure.md) - 基础架构设计
- [v0.2项目架构](../v0.2/project-architecture.md) - AI核心模块架构

## v0.3新增架构组件

### 1. 工作空间管理层

工作空间是数据表的组织容器，提供权限管理和协作功能。

```
工作空间管理层
├── 工作空间服务 (WorkspaceService) - 工作空间CRUD操作
├── 成员管理服务 (MemberService) - 工作空间成员管理
├── 权限控制服务 (PermissionService) - 基于角色的权限控制
└── 协作功能服务 (CollaborationService) - 多用户协作功能
```

### 2. 数据表设计层

```
数据表设计层
├── 表结构管理 (TableStructureService)
├── 字段类型系统 (FieldTypeSystem)
├── 验证规则引擎 (ValidationEngine)
└── 模板管理系统 (TemplateService)
```

### 3. AI辅助设计层

```
AI辅助设计层
├── 表结构生成服务 (TableGenerationService)
├── 字段推荐服务 (FieldRecommendationService)
├── 提示词生成服务 (PromptGenerationService)
└── AI历史管理 (AIHistoryService)
```

## 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Vue 3)                        │
├─────────────────────────────────────────────────────────────┤
│  用户认证  │  工作空间管理  │  数据表设计器  │  AI助手  │  OCR处理  │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    API网关层 (Spring Boot)                   │
├─────────────────────────────────────────────────────────────┤
│     认证API    │   工作空间API   │   数据表API   │   AI API    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                       业务服务层                            │
├─────────────────────────────────────────────────────────────┤
│ 用户服务 │ 工作空间服务 │ 表结构服务 │ AI服务 │ OCR服务 │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                       数据访问层                            │
├─────────────────────────────────────────────────────────────┤
│    用户数据    │   工作空间数据   │   表结构数据   │   AI数据    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                       存储层                               │
├─────────────────────────────────────────────────────────────┤
│           MySQL 8.0           │         Redis 6.0+         │
└─────────────────────────────────────────────────────────────┘
```

## 新增技术组件

### 1. JSON Schema验证

```java
@Component
public class JsonSchemaValidator {
    
    public ValidationResult validateTableSchema(String schema) {
        // 验证表结构JSON Schema
    }
    
    public ValidationResult validateFieldOptions(String fieldType, String options) {
        // 验证字段选项配置
    }
}
```

### 2. 动态表单生成

```javascript
// 前端动态表单生成器
export class DynamicFormGenerator {
    generateForm(tableSchema) {
        // 根据表结构生成动态表单
    }
    
    validateFormData(formData, validationRules) {
        // 根据验证规则验证表单数据
    }
}
```

### 3. AI服务集成框架

```java
@Service
public class AIServiceIntegration {
    
    @Autowired
    private OpenAIClient openAIClient;
    
    @Autowired
    private QwenClient qwenClient;
    
    public AIResponse generateTableStructure(String prompt) {
        // 调用AI服务生成表结构
    }
    
    public AIResponse generateFieldSuggestions(String context) {
        // 生成字段建议
    }
}
```

### 4. 表结构版本控制

```java
@Entity
public class TableVersion {
    private Long id;
    private Long tableId;
    private Integer version;
    private String schema;
    private String changeLog;
    private LocalDateTime createdAt;
    
    // 版本控制逻辑
}
```

## 核心特性实现

### 1. 灵活的字段类型系统

支持多种字段类型：
- **基础类型**: text, number, date, datetime, boolean
- **高级类型**: file, url, email, phone
- **选择类型**: select, multiselect
- **自定义类型**: 支持扩展新的字段类型

### 2. 可视化表设计器

```vue
<template>
  <div class="table-designer">
    <TableInfo v-model="tableInfo" />
    <FieldList 
      :fields="fields" 
      @add="addField"
      @edit="editField"
      @delete="deleteField"
      @reorder="reorderFields"
    />
    <AIAssistant 
      :table-info="tableInfo"
      @suggest="handleSuggestions"
    />
  </div>
</template>
```

### 3. 智能推荐引擎

基于AI的智能推荐：
- **表结构推荐**: 根据表名和描述推荐字段
- **字段类型推荐**: 根据字段名推荐合适的类型
- **验证规则推荐**: 根据字段类型推荐验证规则
- **提示词生成**: 为AI抽取生成优化的提示词

### 4. 实时预览功能

```javascript
export class TablePreview {
    generatePreview(tableSchema) {
        return {
            html: this.generateHTML(tableSchema),
            json: this.generateJSON(tableSchema),
            sql: this.generateSQL(tableSchema)
        };
    }
}
```

## 部署架构

### 开发环境

```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: ai_fdb_dev
      MYSQL_ROOT_PASSWORD: password
    ports:
      - "3306:3306"
  
  redis:
    image: redis:6.2
    ports:
      - "6379:6379"
  
  backend:
    build: ./backend
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
  
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
```

### 生产环境

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │────│   Web Servers   │────│  App Servers    │
│    (Nginx)      │    │   (Nginx)       │    │ (Spring Boot)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Cache Layer   │    │   Database      │
                       │    (Redis)      │    │   (MySQL)       │
                       └─────────────────┘    └─────────────────┘
```

## 性能优化策略

### 1. 数据库优化
- 合理的索引设计
- 查询语句优化
- 连接池配置
- 读写分离

### 2. 缓存策略
- Redis缓存热点数据
- 表结构缓存
- 用户会话缓存
- AI生成结果缓存

### 3. 前端优化
- 组件懒加载
- 虚拟滚动
- 防抖和节流
- 资源压缩

### 4. AI服务优化
- 请求合并
- 结果缓存
- 异步处理
- 错误重试

## 安全设计

### 1. 工作空间权限控制

```java
@PreAuthorize("@workspacePermissionService.hasPermission(#workspaceId, authentication.name, 'CREATE_TABLE')")
public ResponseEntity<?> createTable(@PathVariable Long workspaceId, @RequestBody CreateTableRequest request) {
    // 创建数据表逻辑
}
```

### 2. 数据访问控制

- 基于角色的访问控制(RBAC)
- 工作空间级别的权限隔离
- 数据表级别的访问控制
- 字段级别的权限管理

### 3. API安全

- JWT Token认证
- 请求频率限制
- 输入数据验证
- SQL注入防护

## 监控和日志

### 1. 应用监控
- 性能指标监控
- 错误率监控
- 响应时间监控
- 资源使用监控

### 2. 业务监控
- 用户行为分析
- 功能使用统计
- AI服务调用统计
- 表结构创建统计

### 3. 日志管理
- 结构化日志
- 日志级别控制
- 日志聚合分析
- 错误日志告警
