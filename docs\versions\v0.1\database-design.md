# AI-FDB v0.1 - 数据库设计

## 数据库架构概述

v0.1版本采用MySQL 8.0作为主数据库，Redis作为缓存和会话存储。数据库设计遵循第三范式，确保数据一致性和完整性。

### 数据库配置

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS aifdb_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS aifdb_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS aifdb_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER IF NOT EXISTS 'aifdb_user'@'%' IDENTIFIED BY 'aifdb_password_2024';
GRANT ALL PRIVILEGES ON aifdb_*.* TO 'aifdb_user'@'%';
FLUSH PRIVILEGES;
```

## 核心数据表设计

### 1. 用户表 (users)

用户表是系统的核心表，存储所有用户的基本信息和认证相关数据。

```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID，主键',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名，唯一',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱地址，唯一',
    phone VARCHAR(20) COMMENT '手机号码',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值，BCrypt加密',
    avatar_url VARCHAR(500) COMMENT '头像URL地址',
    status TINYINT DEFAULT 1 COMMENT '用户状态：1-正常，0-禁用，2-待验证',
    role ENUM('guest', 'user', 'admin') DEFAULT 'user' COMMENT '用户角色',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否已验证',
    phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机号是否已验证',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    login_count INT DEFAULT 0 COMMENT '登录次数统计',
    failed_login_attempts INT DEFAULT 0 COMMENT '连续登录失败次数',
    locked_until TIMESTAMP NULL COMMENT '账户锁定截止时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_role (role),
    INDEX idx_created_at (created_at),
    INDEX idx_last_login (last_login_at),
    INDEX idx_email_verified (email_verified),
    
    -- 复合索引
    INDEX idx_status_role (status, role),
    INDEX idx_login_status (status, last_login_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基本信息表';
```

#### 字段说明

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 用户唯一标识 | 主键，自增 |
| username | VARCHAR(50) | 用户名 | 唯一，非空 |
| email | VARCHAR(100) | 邮箱地址 | 唯一，非空 |
| phone | VARCHAR(20) | 手机号码 | 可空 |
| password_hash | VARCHAR(255) | 密码哈希 | 非空，BCrypt加密 |
| avatar_url | VARCHAR(500) | 头像URL | 可空 |
| status | TINYINT | 用户状态 | 默认1（正常） |
| role | ENUM | 用户角色 | 默认'user' |
| email_verified | BOOLEAN | 邮箱验证状态 | 默认FALSE |
| phone_verified | BOOLEAN | 手机验证状态 | 默认FALSE |
| last_login_at | TIMESTAMP | 最后登录时间 | 可空 |
| login_count | INT | 登录次数 | 默认0 |
| failed_login_attempts | INT | 失败登录次数 | 默认0 |
| locked_until | TIMESTAMP | 锁定截止时间 | 可空 |

### 2. 用户会话表 (user_sessions)

用户会话表存储用户的登录会话信息，支持多设备登录和会话管理。

```sql
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '会话ID，主键',
    user_id BIGINT NOT NULL COMMENT '用户ID，外键',
    session_token VARCHAR(255) UNIQUE NOT NULL COMMENT '会话令牌，JWT Token',
    refresh_token VARCHAR(255) UNIQUE NOT NULL COMMENT '刷新令牌',
    device_id VARCHAR(100) COMMENT '设备唯一标识',
    device_name VARCHAR(100) COMMENT '设备名称',
    device_type ENUM('web', 'mobile', 'desktop', 'tablet') DEFAULT 'web' COMMENT '设备类型',
    ip_address VARCHAR(45) COMMENT 'IP地址，支持IPv6',
    user_agent TEXT COMMENT '用户代理字符串',
    location VARCHAR(100) COMMENT '登录地理位置',
    expires_at TIMESTAMP NOT NULL COMMENT 'Token过期时间',
    refresh_expires_at TIMESTAMP NOT NULL COMMENT '刷新Token过期时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '会话是否活跃',
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 索引设计
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_refresh_token (refresh_token),
    INDEX idx_device_id (device_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active),
    INDEX idx_last_activity (last_activity_at),
    
    -- 复合索引
    INDEX idx_user_active (user_id, is_active, expires_at),
    INDEX idx_user_device (user_id, device_id),
    INDEX idx_active_sessions (is_active, expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话管理表';
```

#### 字段说明

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 会话唯一标识 | 主键，自增 |
| user_id | BIGINT | 关联用户ID | 外键，非空 |
| session_token | VARCHAR(255) | JWT访问令牌 | 唯一，非空 |
| refresh_token | VARCHAR(255) | 刷新令牌 | 唯一，非空 |
| device_id | VARCHAR(100) | 设备标识 | 可空 |
| device_name | VARCHAR(100) | 设备名称 | 可空 |
| device_type | ENUM | 设备类型 | 默认'web' |
| ip_address | VARCHAR(45) | 登录IP地址 | 可空 |
| user_agent | TEXT | 浏览器信息 | 可空 |
| location | VARCHAR(100) | 登录位置 | 可空 |
| expires_at | TIMESTAMP | Token过期时间 | 非空 |
| refresh_expires_at | TIMESTAMP | 刷新Token过期时间 | 非空 |
| is_active | BOOLEAN | 会话状态 | 默认TRUE |

### 3. 系统配置表 (system_configs)

系统配置表存储系统级别的配置参数，支持动态配置管理。

```sql
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID，主键',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键名',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    category VARCHAR(50) DEFAULT 'general' COMMENT '配置分类',
    description VARCHAR(255) COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开配置',
    is_editable BOOLEAN DEFAULT TRUE COMMENT '是否可编辑',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引设计
    INDEX idx_config_key (config_key),
    INDEX idx_category (category),
    INDEX idx_is_public (is_public),
    INDEX idx_is_editable (is_editable)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';
```

## 初始化数据

### 系统配置初始化

```sql
-- 插入系统基础配置
INSERT INTO system_configs (config_key, config_value, config_type, category, description, is_public) VALUES
('system.name', 'AI-FDB', 'string', 'system', '系统名称', true),
('system.version', '0.1.0', 'string', 'system', '系统版本', true),
('system.description', 'AI文件数据管理系统', 'string', 'system', '系统描述', true),
('system.logo_url', '/assets/logo.png', 'string', 'system', '系统Logo地址', true),

-- 认证相关配置
('auth.jwt.secret', 'ai-fdb-jwt-secret-key-2024', 'string', 'auth', 'JWT密钥', false),
('auth.jwt.expiration', '86400', 'number', 'auth', 'JWT过期时间(秒)', false),
('auth.jwt.refresh_expiration', '604800', 'number', 'auth', 'JWT刷新过期时间(秒)', false),
('auth.password.min_length', '8', 'number', 'auth', '密码最小长度', false),
('auth.password.require_special', 'true', 'boolean', 'auth', '密码是否需要特殊字符', false),
('auth.login.max_attempts', '5', 'number', 'auth', '最大登录尝试次数', false),
('auth.login.lock_duration', '1800', 'number', 'auth', '账户锁定时长(秒)', false),

-- 会话管理配置
('session.max_concurrent', '5', 'number', 'session', '最大并发会话数', false),
('session.cleanup_interval', '3600', 'number', 'session', '会话清理间隔(秒)', false),

-- 文件上传配置
('file.max_size', '104857600', 'number', 'file', '最大文件大小(100MB)', false),
('file.allowed_types', '["jpg","jpeg","png","gif","pdf","doc","docx","txt"]', 'json', 'file', '允许的文件类型', false)

ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    updated_at = CURRENT_TIMESTAMP;
```

### 默认用户初始化

```sql
-- 创建默认管理员用户 (密码: admin123456)
INSERT INTO users (username, email, password_hash, role, status, email_verified) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjnZr7I7YjSaa', 'admin', 1, true)
ON DUPLICATE KEY UPDATE 
    password_hash = VALUES(password_hash),
    updated_at = CURRENT_TIMESTAMP;

-- 创建测试用户 (密码: test123456)
INSERT INTO users (username, email, password_hash, role, status, email_verified) VALUES
('testuser', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 1, true)
ON DUPLICATE KEY UPDATE 
    password_hash = VALUES(password_hash),
    updated_at = CURRENT_TIMESTAMP;
```

## 数据库索引优化

### 查询优化索引

```sql
-- 用户登录查询优化
CREATE INDEX idx_users_login ON users(email, status) USING BTREE;
CREATE INDEX idx_users_username_login ON users(username, status) USING BTREE;

-- 会话查询优化
CREATE INDEX idx_sessions_user_active ON user_sessions(user_id, is_active, expires_at) USING BTREE;
CREATE INDEX idx_sessions_token_valid ON user_sessions(session_token, expires_at) USING BTREE;

-- 系统配置查询优化
CREATE INDEX idx_configs_category_public ON system_configs(category, is_public) USING BTREE;
```

## Redis缓存设计

### 缓存键命名规范

```
# 用户相关缓存
user:info:{user_id}                 # 用户基本信息
user:sessions:{user_id}             # 用户会话列表
user:login_attempts:{email}         # 登录失败次数

# 会话相关缓存
session:token:{token}               # Token信息
session:refresh:{refresh_token}     # 刷新Token信息

# 系统配置缓存
config:all                          # 所有配置
config:public                       # 公开配置
config:category:{category}          # 分类配置
```

### 缓存过期策略

| 缓存类型 | 过期时间 | 说明 |
|----------|----------|------|
| 用户信息 | 1小时 | 用户基本信息缓存 |
| 会话信息 | Token过期时间 | 与Token同步过期 |
| 登录失败次数 | 30分钟 | 防暴力破解 |
| 系统配置 | 24小时 | 系统配置缓存 |

## 数据备份策略

### 备份计划

```bash
# 每日全量备份
0 2 * * * mysqldump --single-transaction --routines --triggers aifdb_prod > /backup/aifdb_$(date +\%Y\%m\%d).sql

# 每小时增量备份（二进制日志）
0 * * * * mysqlbinlog --start-datetime="$(date -d '1 hour ago' '+\%Y-\%m-\%d \%H:00:00')" /var/log/mysql/mysql-bin.* > /backup/incremental/aifdb_$(date +\%Y\%m\%d\%H).sql

# 备份文件清理（保留30天）
0 3 * * * find /backup -name "aifdb_*.sql" -mtime +30 -delete
```

### 恢复策略

```bash
# 全量恢复
mysql aifdb_prod < /backup/aifdb_20241222.sql

# 增量恢复
mysql aifdb_prod < /backup/incremental/aifdb_2024122214.sql
```

## 数据安全

### 敏感数据处理

1. **密码安全**
   - 使用BCrypt加密存储
   - 加盐处理，防止彩虹表攻击
   - 密码强度验证

2. **个人信息保护**
   - 邮箱和手机号脱敏显示
   - 敏感操作日志记录
   - 数据访问权限控制

3. **数据传输安全**
   - HTTPS加密传输
   - API接口认证
   - 请求签名验证

### 数据完整性

1. **外键约束**
   - 确保数据关联完整性
   - 级联删除策略

2. **数据验证**
   - 字段长度限制
   - 数据格式验证
   - 业务规则约束

3. **事务管理**
   - ACID特性保证
   - 分布式事务处理
   - 死锁检测和处理
