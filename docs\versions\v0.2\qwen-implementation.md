# AI-FDB v0.2 - 通义千问技术实施文档

## 📚 官方文档参考

### 通义千问官方文档地址
- **主要文档**: https://help.aliyun.com/zh/model-studio/use-qwen-by-calling-api
- **API调用指南**: https://help.aliyun.com/zh/model-studio/first-api-call-to-qwen
- **DashScope文档**: https://help.aliyun.com/zh/model-studio/developer-reference/api-details
- **Python SDK**: https://help.aliyun.com/zh/model-studio/developer-reference/use-qwen-by-calling-api
- **模型列表**: https://help.aliyun.com/zh/model-studio/getting-started/models

### 查询时间记录
- **查询日期**: 2024年12月22日
- **文档版本**: DashScope API v1
- **核心模型**: qwen-turbo
- **API密钥**: sk-beff2b8bc208457a9d971610488661f0

## 🎯 通义千问集成概述

### 技术选型理由
1. **中文优化**: 专门针对中文语义理解优化
2. **高性能**: qwen-turbo模型响应速度快
3. **成本可控**: 按Token计费，成本透明
4. **API稳定**: 阿里云提供的企业级API服务
5. **功能丰富**: 支持多轮对话、函数调用等高级功能

### 核心能力架构
```
通义千问 qwen-turbo 处理流程:
文本输入 → 语义理解 → 字段抽取 → 置信度评估 → 结果输出
    ↓         ↓         ↓         ↓         ↓
预处理    上下文分析  实体识别   可信度计算  后处理
```

## 🔧 环境配置和安装

### 1. 系统要求
```bash
# Python环境要求
- Python 3.8+
- pip 20.0+

# 网络要求
- 稳定的互联网连接
- 支持HTTPS请求

# API要求
- 有效的阿里云账号
- DashScope API密钥
```

### 2. 安装步骤

#### 2.1 DashScope SDK安装
```bash
# 1. 安装DashScope SDK
pip install dashscope -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 2. 安装HTTP请求依赖
pip install requests -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 3. 安装JSON处理依赖
pip install jsonschema -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 2.2 API密钥配置
```bash
# 方式1: 环境变量配置(推荐)
export DASHSCOPE_API_KEY="sk-beff2b8bc208457a9d971610488661f0"

# 方式2: 配置文件
echo "DASHSCOPE_API_KEY=sk-beff2b8bc208457a9d971610488661f0" > .env

# 方式3: 代码中直接配置(不推荐用于生产环境)
```

#### 2.3 连接测试
```python
# test_qwen_connection.py
import os
from dashscope import Generation

def test_qwen_connection():
    """测试通义千问API连接"""
    try:
        response = Generation.call(
            api_key=os.getenv('DASHSCOPE_API_KEY'),
            model='qwen-turbo',
            messages=[
                {'role': 'user', 'content': '你好，请介绍一下你自己'}
            ],
            result_format='message'
        )

        if response.status_code == 200:
            print("✅ 通义千问API连接成功")
            print(f"响应内容: {response.output.choices[0].message.content}")
            return True
        else:
            print(f"❌ API调用失败: {response.code} - {response.message}")
            return False

    except Exception as e:
        print(f"❌ 连接测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_qwen_connection()
```

## 🚀 核心功能实现

### 1. 语义抽取服务

```python
# scripts/qwen_extraction_service.py
import sys
import json
import os
import time
import re
from dashscope import Generation
from typing import Dict, List, Any, Optional

class QwenExtractionService:
    def __init__(self, api_key: str = None, config: Dict = None):
        """初始化通义千问抽取服务"""
        self.api_key = api_key or os.getenv('DASHSCOPE_API_KEY')
        if not self.api_key:
            raise ValueError("DASHSCOPE_API_KEY 未设置")

        self.config = config or {}
        self.model = self.config.get('model', 'qwen-turbo')
        self.max_tokens = self.config.get('max_tokens', 4000)
        self.temperature = self.config.get('temperature', 0.1)
        self.top_p = self.config.get('top_p', 0.8)

    def extract_fields(self, text: str, fields: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        使用通义千问进行字段抽取

        Args:
            text: 待抽取的文本内容
            fields: 字段配置列表

        Returns:
            抽取结果字典
        """
        try:
            # 构建提示词
            system_prompt = self._build_system_prompt(fields)
            user_prompt = self._build_user_prompt(text)

            # 调用API
            response = Generation.call(
                api_key=self.api_key,
                model=self.model,
                messages=[
                    {'role': 'system', 'content': system_prompt},
                    {'role': 'user', 'content': user_prompt}
                ],
                result_format='message',
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                top_p=self.top_p
            )

            if response.status_code == 200:
                content = response.output.choices[0].message.content
                return self._parse_response(content, response)
            else:
                raise Exception(f"API调用失败: {response.code} - {response.message}")

        except Exception as e:
            raise Exception(f"通义千问API调用错误: {str(e)}")

    def _build_system_prompt(self, fields: List[Dict[str, Any]]) -> str:
        """构建系统提示词"""
        field_descriptions = []

        for field in fields:
            field_name = field.get('field_name', field.get('name', ''))
            field_desc = field.get('field_description', field.get('description', ''))
            extraction_prompt = field.get('extraction_prompt', field.get('prompt', ''))
            field_type = field.get('field_type', 'text')

            field_descriptions.append(f"""
字段名: {field_name}
字段类型: {field_type}
字段描述: {field_desc}
抽取规则: {extraction_prompt}
""")

        system_prompt = f"""你是一个专业的文档信息抽取助手，擅长从各种文档中准确抽取指定的字段信息。

## 抽取要求：
1. 严格按照字段抽取规则进行抽取
2. 如果某个字段在文本中找不到对应信息，返回null
3. 抽取结果必须是标准JSON格式
4. 对于日期字段，尽量统一格式为YYYY-MM-DD或YYYY年MM月DD日
5. 对于数字字段，保留原始格式和单位
6. 对于文本字段，保持原文准确性，去除多余空格
7. 为每个字段提供置信度评估(0-1之间的小数)

## 需要抽取的字段：
{chr(10).join(field_descriptions)}

## 输出格式要求：
请严格按照以下JSON格式返回结果，不要添加任何其他内容：

```json
{{
    "extracted_data": {{
        "字段名1": "抽取值1",
        "字段名2": "抽取值2"
    }},
    "field_confidence": {{
        "字段名1": 0.95,
        "字段名2": 0.88
    }},
    "extraction_notes": "抽取过程中的注意事项或说明"
}}
```"""

        return system_prompt

    def _build_user_prompt(self, text: str) -> str:
        """构建用户提示词"""
        # 文本预处理
        cleaned_text = self._preprocess_text(text)

        user_prompt = f"""请从以下文本中抽取指定字段的信息：

## 文本内容：
{cleaned_text}

请按照系统要求的JSON格式返回抽取结果。"""

        return user_prompt

    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 去除多余空白字符
        text = re.sub(r'\s+', ' ', text.strip())

        # 限制文本长度(避免超出Token限制)
        max_length = 8000  # 预留空间给提示词
        if len(text) > max_length:
            text = text[:max_length] + "...(文本已截断)"

        return text

    def _parse_response(self, content: str, response) -> Dict[str, Any]:
        """解析API响应"""
        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析整个内容
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                if start_idx != -1 and end_idx != 0:
                    json_str = content[start_idx:end_idx]
                else:
                    raise ValueError("未找到有效的JSON格式")

            # 解析JSON
            result = json.loads(json_str)

            # 验证结果格式
            if not isinstance(result, dict):
                raise ValueError("返回结果不是字典格式")

            # 确保必要字段存在
            if 'extracted_data' not in result:
                result['extracted_data'] = {}
            if 'field_confidence' not in result:
                result['field_confidence'] = {}
            if 'extraction_notes' not in result:
                result['extraction_notes'] = '自动抽取完成'

            # 添加API使用统计
            if hasattr(response, 'usage'):
                result['token_usage'] = {
                    'input_tokens': getattr(response.usage, 'input_tokens', 0),
                    'output_tokens': getattr(response.usage, 'output_tokens', 0),
                    'total_tokens': getattr(response.usage, 'total_tokens', 0)
                }

            return result

        except json.JSONDecodeError as e:
            # JSON解析失败，返回原始内容
            return {
                'extracted_data': {},
                'field_confidence': {},
                'extraction_notes': f'JSON解析失败: {str(e)}',
                'raw_response': content,
                'parse_error': True
            }
        except Exception as e:
            return {
                'extracted_data': {},
                'field_confidence': {},
                'extraction_notes': f'响应解析错误: {str(e)}',
                'raw_response': content,
                'parse_error': True
            }

def main():
    """命令行入口"""
    if len(sys.argv) < 3:
        print(json.dumps({
            'success': False,
            'error': 'Usage: python qwen_extraction_service.py <text> <fields_json>',
            'error_type': 'ArgumentError'
        }, ensure_ascii=False))
        sys.exit(1)

    text = sys.argv[1]
    fields_json = sys.argv[2]

    start_time = time.time()

    try:
        # 解析字段配置
        fields = json.loads(fields_json)

        # 初始化服务
        service = QwenExtractionService()

        # 执行抽取
        result = service.extract_fields(text, fields)

        # 计算处理时间
        processing_time = round((time.time() - start_time) * 1000)

        # 构建输出结果
        output = {
            'success': True,
            'ai_model': service.model,
            'processing_time': processing_time,
            'input_text_length': len(text),
            'fields_count': len(fields),
            'extracted_data': result.get('extracted_data', {}),
            'field_confidence': result.get('field_confidence', {}),
            'extraction_notes': result.get('extraction_notes', ''),
            'token_usage': result.get('token_usage', {}),
            'raw_response': result.get('raw_response', ''),
            'parse_error': result.get('parse_error', False),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        print(json.dumps(output, ensure_ascii=False, indent=2))

    except Exception as e:
        processing_time = round((time.time() - start_time) * 1000)
        error_output = {
            'success': False,
            'error': str(e),
            'error_type': type(e).__name__,
            'processing_time': processing_time,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        print(json.dumps(error_output, ensure_ascii=False))
        sys.exit(1)

if __name__ == "__main__":
    main()
```

### 2. 高级功能实现

#### 2.1 智能字段推荐
```python
# scripts/field_recommendation_service.py
from qwen_extraction_service import QwenExtractionService

class FieldRecommendationService:
    def __init__(self, qwen_service: QwenExtractionService):
        self.qwen_service = qwen_service

    def recommend_fields(self, text: str, document_type: str = None) -> List[Dict]:
        """基于文档内容推荐抽取字段"""

        system_prompt = f"""你是一个文档分析专家，请分析给定的文档内容，推荐适合抽取的字段。

## 分析要求：
1. 识别文档类型和主要内容
2. 推荐5-10个最有价值的抽取字段
3. 为每个字段提供清晰的描述和抽取规则
4. 考虑字段的实用性和可抽取性

## 输出格式：
```json
{{
    "document_type": "文档类型",
    "recommended_fields": [
        {{
            "field_name": "字段名称",
            "field_description": "字段描述",
            "extraction_prompt": "抽取规则",
            "field_type": "text|number|date|boolean",
            "importance": "high|medium|low",
            "extractability": "easy|medium|hard"
        }}
    ]
}}
```"""

        user_prompt = f"""请分析以下文档内容并推荐抽取字段：

文档类型提示: {document_type or '未知'}

文档内容：
{text[:2000]}"""  # 限制长度

        try:
            response = Generation.call(
                api_key=self.qwen_service.api_key,
                model=self.qwen_service.model,
                messages=[
                    {'role': 'system', 'content': system_prompt},
                    {'role': 'user', 'content': user_prompt}
                ],
                result_format='message',
                max_tokens=2000,
                temperature=0.3
            )

            if response.status_code == 200:
                content = response.output.choices[0].message.content
                return self._parse_field_recommendations(content)
            else:
                return []

        except Exception as e:
            print(f"字段推荐失败: {str(e)}")
            return []

    def _parse_field_recommendations(self, content: str) -> List[Dict]:
        """解析字段推荐结果"""
        try:
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group(1))
                return result.get('recommended_fields', [])
        except:
            pass
        return []
```

#### 2.2 批量抽取处理
```python
# scripts/batch_extraction_service.py
import concurrent.futures
from typing import List, Dict, Any

class BatchExtractionService:
    def __init__(self, qwen_service: QwenExtractionService, max_workers: int = 3):
        self.qwen_service = qwen_service
        self.max_workers = max_workers

    def batch_extract(self, texts: List[str], fields: List[Dict[str, Any]]) -> List[Dict]:
        """批量执行语义抽取"""
        results = []

        # 并发处理(注意API限流)
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_index = {
                executor.submit(self.qwen_service.extract_fields, text, fields): i
                for i, text in enumerate(texts)
            }

            for future in concurrent.futures.as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    result['batch_index'] = index
                    results.append(result)
                except Exception as e:
                    results.append({
                        'batch_index': index,
                        'success': False,
                        'error': str(e),
                        'extracted_data': {},
                        'field_confidence': {}
                    })

        # 按索引排序
        results.sort(key=lambda x: x.get('batch_index', 0))
        return results
```

#### 2.3 抽取质量评估
```python
# scripts/extraction_quality_evaluator.py
class ExtractionQualityEvaluator:
    def __init__(self):
        self.quality_thresholds = {
            'high': 0.9,
            'medium': 0.7,
            'low': 0.5
        }

    def evaluate_extraction_quality(self, extraction_result: Dict) -> Dict:
        """评估抽取质量"""
        if not extraction_result.get('success', False):
            return {
                'overall_quality': 'failed',
                'quality_score': 0.0,
                'issues': ['抽取失败']
            }

        extracted_data = extraction_result.get('extracted_data', {})
        field_confidence = extraction_result.get('field_confidence', {})

        issues = []
        quality_factors = []

        # 1. 字段完整性评估
        total_fields = len(extracted_data)
        non_null_fields = len([v for v in extracted_data.values() if v is not None and v != ''])

        if total_fields > 0:
            completeness = non_null_fields / total_fields
            quality_factors.append(completeness)

            if completeness < 0.5:
                issues.append('字段完整性较低')
        else:
            quality_factors.append(0)
            issues.append('未抽取到任何字段')

        # 2. 置信度评估
        if field_confidence:
            avg_confidence = sum(field_confidence.values()) / len(field_confidence)
            quality_factors.append(avg_confidence)

            if avg_confidence < 0.7:
                issues.append('平均置信度较低')
        else:
            quality_factors.append(0.5)
            issues.append('缺少置信度信息')

        # 3. 数据格式评估
        format_score = self._evaluate_data_format(extracted_data)
        quality_factors.append(format_score)

        if format_score < 0.8:
            issues.append('数据格式存在问题')

        # 4. 响应时间评估
        processing_time = extraction_result.get('processing_time', 0)
        time_score = max(0, 1 - (processing_time - 5000) / 10000)  # 5秒为基准
        quality_factors.append(max(0, time_score))

        if processing_time > 10000:
            issues.append('处理时间过长')

        # 计算综合质量分数
        overall_score = sum(quality_factors) / len(quality_factors)

        # 确定质量等级
        if overall_score >= self.quality_thresholds['high']:
            quality_level = 'high'
        elif overall_score >= self.quality_thresholds['medium']:
            quality_level = 'medium'
        elif overall_score >= self.quality_thresholds['low']:
            quality_level = 'low'
        else:
            quality_level = 'poor'

        return {
            'overall_quality': quality_level,
            'quality_score': round(overall_score, 3),
            'completeness_score': quality_factors[0] if quality_factors else 0,
            'confidence_score': quality_factors[1] if len(quality_factors) > 1 else 0,
            'format_score': quality_factors[2] if len(quality_factors) > 2 else 0,
            'performance_score': quality_factors[3] if len(quality_factors) > 3 else 0,
            'issues': issues,
            'recommendations': self._generate_recommendations(issues)
        }

    def _evaluate_data_format(self, extracted_data: Dict) -> float:
        """评估数据格式质量"""
        if not extracted_data:
            return 0.0

        format_scores = []

        for field_name, value in extracted_data.items():
            if value is None or value == '':
                format_scores.append(0.5)  # 空值
                continue

            # 根据字段名推断类型并验证格式
            if '日期' in field_name or 'date' in field_name.lower():
                score = self._validate_date_format(str(value))
            elif '金额' in field_name or '价格' in field_name or 'amount' in field_name.lower():
                score = self._validate_number_format(str(value))
            elif '电话' in field_name or '手机' in field_name or 'phone' in field_name.lower():
                score = self._validate_phone_format(str(value))
            else:
                score = self._validate_text_format(str(value))

            format_scores.append(score)

        return sum(format_scores) / len(format_scores) if format_scores else 0.0

    def _validate_date_format(self, value: str) -> float:
        """验证日期格式"""
        import re
        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',           # 2024-12-22
            r'\d{4}年\d{1,2}月\d{1,2}日',    # 2024年12月22日
            r'\d{4}/\d{2}/\d{2}',           # 2024/12/22
            r'\d{2}/\d{2}/\d{4}'            # 22/12/2024
        ]

        for pattern in date_patterns:
            if re.search(pattern, value):
                return 1.0

        return 0.3  # 包含日期相关词汇但格式不标准

    def _validate_number_format(self, value: str) -> float:
        """验证数字格式"""
        import re
        # 检查是否包含数字和可能的货币符号
        if re.search(r'\d+', value):
            return 1.0 if re.search(r'[\d,.]+(元|万|亿|$|￥|USD|CNY)?', value) else 0.8
        return 0.2

    def _validate_phone_format(self, value: str) -> float:
        """验证电话格式"""
        import re
        phone_patterns = [
            r'1[3-9]\d{9}',                 # 手机号
            r'\d{3,4}-\d{7,8}',             # 固定电话
            r'\+86\s*1[3-9]\d{9}'           # 国际格式手机号
        ]

        for pattern in phone_patterns:
            if re.search(pattern, value):
                return 1.0

        return 0.3 if re.search(r'\d{7,}', value) else 0.1

    def _validate_text_format(self, value: str) -> float:
        """验证文本格式"""
        if len(value.strip()) == 0:
            return 0.0
        elif len(value.strip()) < 2:
            return 0.5
        elif len(value.strip()) > 100:
            return 0.8  # 可能过长
        else:
            return 1.0

    def _generate_recommendations(self, issues: List[str]) -> List[str]:
        """生成改进建议"""
        recommendations = []

        if '字段完整性较低' in issues:
            recommendations.append('优化抽取提示词，提高字段识别率')

        if '平均置信度较低' in issues:
            recommendations.append('检查文本质量，考虑使用更高精度的OCR')

        if '数据格式存在问题' in issues:
            recommendations.append('完善字段验证规则和格式化处理')

        if '处理时间过长' in issues:
            recommendations.append('优化提示词长度，考虑文本预处理')

        return recommendations
```

## ⚙️ 配置优化

### 1. 模型参数优化
```python
# 不同场景的配置模板
config_templates = {
    'high_accuracy': {
        'model': 'qwen-turbo',
        'temperature': 0.1,      # 低温度，提高准确性
        'top_p': 0.8,
        'max_tokens': 4000,
        'repetition_penalty': 1.1
    },
    'balanced': {
        'model': 'qwen-turbo',
        'temperature': 0.3,      # 平衡准确性和创造性
        'top_p': 0.9,
        'max_tokens': 3000,
        'repetition_penalty': 1.0
    },
    'fast_response': {
        'model': 'qwen-turbo',
        'temperature': 0.5,      # 稍高温度，加快响应
        'top_p': 0.95,
        'max_tokens': 2000,
        'repetition_penalty': 1.0
    }
}
```

### 2. 提示词模板优化
```python
# 针对不同文档类型的提示词模板
prompt_templates = {
    'contract': {
        'system_prefix': '你是一个专业的合同分析专家',
        'extraction_focus': '重点关注合同条款、当事人信息、金额和日期',
        'validation_rules': '确保法律术语的准确性'
    },
    'invoice': {
        'system_prefix': '你是一个专业的财务票据分析专家',
        'extraction_focus': '重点关注发票号码、金额、税额和开票信息',
        'validation_rules': '确保数字格式的准确性'
    },
    'identity': {
        'system_prefix': '你是一个专业的身份证件分析专家',
        'extraction_focus': '重点关注个人信息、证件号码和有效期',
        'validation_rules': '确保个人信息的隐私保护'
    }
}
```

## 🧪 测试和验证

### 1. 功能测试脚本
```python
# scripts/test_qwen_extraction.py
def test_qwen_extraction():
    """测试通义千问抽取功能"""

    # 测试文本
    test_text = """
    房屋所有权证

    权利人：张维彬
    共有情况：单独所有
    坐落：北京市朝阳区建国路88号
    不动产单元号：110105001001GB00001F00010001
    权利类型：国有建设用地使用权/房屋所有权
    权利性质：出让/市场化商品房
    用途：住宅
    面积：建筑面积120.50平方米
    使用期限：2070年12月31日

    发证日期：2025年5月30日
    """

    # 测试字段配置
    test_fields = [
        {
            'field_name': '权利人',
            'field_description': '房产证权利人姓名',
            'extraction_prompt': '从房产证中提取权利人姓名',
            'field_type': 'text'
        },
        {
            'field_name': '房屋地址',
            'field_description': '房屋坐落地址',
            'extraction_prompt': '从房产证中提取房屋坐落地址',
            'field_type': 'text'
        },
        {
            'field_name': '建筑面积',
            'field_description': '房屋建筑面积',
            'extraction_prompt': '从房产证中提取建筑面积，包含数字和单位',
            'field_type': 'number'
        },
        {
            'field_name': '发证日期',
            'field_description': '房产证发证日期',
            'extraction_prompt': '从房产证中提取发证日期',
            'field_type': 'date'
        }
    ]

    # 执行测试
    service = QwenExtractionService()
    result = service.extract_fields(test_text, test_fields)

    print("=== 通义千问抽取测试结果 ===")
    print(f"抽取成功: {result.get('success', False)}")
    print(f"抽取数据: {json.dumps(result.get('extracted_data', {}), ensure_ascii=False, indent=2)}")
    print(f"置信度: {json.dumps(result.get('field_confidence', {}), ensure_ascii=False, indent=2)}")
    print(f"处理说明: {result.get('extraction_notes', '')}")

    # 质量评估
    evaluator = ExtractionQualityEvaluator()
    quality = evaluator.evaluate_extraction_quality(result)
    print(f"质量评估: {quality}")

if __name__ == "__main__":
    test_qwen_extraction()
```

### 2. 性能基准测试
```python
# scripts/benchmark_qwen.py
import time
import statistics

def benchmark_qwen_performance():
    """通义千问性能基准测试"""

    service = QwenExtractionService()

    # 测试数据
    test_cases = [
        ("短文本", "姓名：张三，电话：13800138000", [{'field_name': '姓名', 'extraction_prompt': '提取姓名'}]),
        ("中等文本", "这是一份包含多个字段的测试文档..." * 10, [{'field_name': '关键词', 'extraction_prompt': '提取关键词'}]),
        ("长文本", "这是一份很长的文档内容..." * 50, [{'field_name': '摘要', 'extraction_prompt': '提取文档摘要'}])
    ]

    results = {}

    for test_name, text, fields in test_cases:
        times = []
        success_count = 0

        print(f"\n测试 {test_name}:")

        for i in range(5):
            start_time = time.time()
            try:
                result = service.extract_fields(text, fields)
                end_time = time.time()

                if result.get('success', False):
                    success_count += 1
                    times.append(end_time - start_time)
                    print(f"  第{i+1}次: {(end_time - start_time)*1000:.0f}ms ✓")
                else:
                    print(f"  第{i+1}次: 失败 ✗")

            except Exception as e:
                print(f"  第{i+1}次: 异常 - {str(e)}")

        if times:
            results[test_name] = {
                'avg_time': statistics.mean(times),
                'min_time': min(times),
                'max_time': max(times),
                'success_rate': success_count / 5
            }

            print(f"  平均时间: {statistics.mean(times)*1000:.0f}ms")
            print(f"  成功率: {success_count}/5")

    return results

if __name__ == "__main__":
    benchmark_qwen_performance()
```

这个通义千问技术文档提供了完整的集成方案，确保v0.2版本能够高效、准确地实现AI语义抽取功能。