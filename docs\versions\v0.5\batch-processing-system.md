# AI-FDB v0.5 - 批量处理系统

## 概述

批量处理系统是v0.5版本的核心基础设施，负责管理大规模文档的批量抽取任务。系统采用分布式架构，支持任务队列、进度监控、错误处理和资源调度，确保高效稳定的批量处理能力。

## 🎯 功能目标

- **任务管理** - 批量任务的创建、调度和监控
- **队列处理** - 基于优先级的任务队列管理
- **进度监控** - 实时任务进度和状态监控
- **错误处理** - 完善的错误检测、记录和恢复机制
- **资源调度** - 智能的资源分配和负载均衡
- **结果管理** - 批量处理结果的汇总和导出

## 🏗️ 系统架构

### 批量处理流程
```
任务创建 → 任务分解 → 队列调度 → 并行处理 → 结果汇总 → 质量检查 → 结果输出
    ↓        ↓        ↓        ↓        ↓        ↓        ↓
  任务配置  文档分组  优先级队列  工作线程  结果合并  质量评估  导出管理
```

### 核心组件
1. **BatchJobManager** - 批量任务管理器
2. **TaskScheduler** - 任务调度器
3. **WorkerPool** - 工作线程池
4. **ProgressMonitor** - 进度监控器
5. **ErrorHandler** - 错误处理器
6. **ResultAggregator** - 结果聚合器

## 📊 数据库设计

### 批量任务表 (batch_jobs)
```sql
CREATE TABLE batch_jobs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    job_name VARCHAR(200) NOT NULL,
    job_type ENUM('document_extraction', 'data_import', 'quality_check') NOT NULL,
    table_id BIGINT NOT NULL,
    total_documents INT DEFAULT 0,
    processed_documents INT DEFAULT 0,
    success_documents INT DEFAULT 0,
    failed_documents INT DEFAULT 0,
    status ENUM('pending', 'running', 'paused', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    priority INT DEFAULT 5 COMMENT '优先级，1-10，数字越大优先级越高',
    config JSON COMMENT '任务配置',
    progress_info JSON COMMENT '进度信息',
    error_summary JSON COMMENT '错误汇总',
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    estimated_completion TIMESTAMP NULL,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at)
);
```

### 文档任务表 (document_tasks)
```sql
CREATE TABLE document_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    batch_job_id BIGINT NOT NULL,
    document_path VARCHAR(500) NOT NULL,
    document_name VARCHAR(255) NOT NULL,
    document_size BIGINT,
    document_type VARCHAR(50),
    status ENUM('pending', 'processing', 'completed', 'failed', 'skipped') DEFAULT 'pending',
    extraction_result JSON COMMENT '抽取结果',
    confidence_scores JSON COMMENT '置信度评分',
    error_message TEXT,
    processing_time INT COMMENT '处理时间（毫秒）',
    retry_count INT DEFAULT 0,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (batch_job_id) REFERENCES batch_jobs(id) ON DELETE CASCADE,
    INDEX idx_batch_job_id (batch_job_id),
    INDEX idx_status (status),
    INDEX idx_document_type (document_type)
);
```

## 🔧 技术实现

### 批量处理配置
```yaml
# application.yml
batch:
  processing:
    max-concurrent-jobs: 5
    max-workers-per-job: 10
    default-priority: 5
    max-retry-attempts: 3
    
  queue:
    type: redis # 支持 memory, redis, rabbitmq
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      database: 2
      
  monitoring:
    progress-update-interval: 5s
    heartbeat-interval: 30s
    cleanup-completed-jobs-after: 7d
    
  resources:
    max-memory-per-worker: 512MB
    max-processing-time: 300s
    enable-auto-scaling: true
```

### 批量任务管理器
```java
@Service
public class BatchJobManager {

    @Autowired
    private BatchJobRepository batchJobRepository;
    
    @Autowired
    private DocumentTaskRepository documentTaskRepository;
    
    @Autowired
    private TaskScheduler taskScheduler;
    
    @Autowired
    private ProgressMonitor progressMonitor;

    public BatchJob createBatchJob(CreateBatchJobRequest request) {
        // 1. 创建批量任务
        BatchJob batchJob = new BatchJob();
        batchJob.setJobName(request.getJobName());
        batchJob.setJobType(request.getJobType());
        batchJob.setTableId(request.getTableId());
        batchJob.setPriority(request.getPriority());
        batchJob.setConfig(request.getConfig());
        batchJob.setCreatedBy(request.getUserId());
        batchJob.setStatus(BatchJobStatus.PENDING);
        
        batchJob = batchJobRepository.save(batchJob);
        
        // 2. 分析文档列表
        List<String> documentPaths = request.getDocumentPaths();
        batchJob.setTotalDocuments(documentPaths.size());
        
        // 3. 创建文档任务
        List<DocumentTask> documentTasks = createDocumentTasks(batchJob.getId(), documentPaths);
        documentTaskRepository.saveAll(documentTasks);
        
        // 4. 提交到调度器
        taskScheduler.submitBatchJob(batchJob);
        
        return batchJob;
    }

    private List<DocumentTask> createDocumentTasks(Long batchJobId, List<String> documentPaths) {
        return documentPaths.stream()
            .map(path -> {
                DocumentTask task = new DocumentTask();
                task.setBatchJobId(batchJobId);
                task.setDocumentPath(path);
                task.setDocumentName(Paths.get(path).getFileName().toString());
                task.setDocumentSize(getFileSize(path));
                task.setDocumentType(detectDocumentType(path));
                task.setStatus(DocumentTaskStatus.PENDING);
                return task;
            })
            .collect(Collectors.toList());
    }

    public void pauseBatchJob(Long jobId) {
        BatchJob job = batchJobRepository.findById(jobId)
            .orElseThrow(() -> new EntityNotFoundException("批量任务不存在"));
        
        job.setStatus(BatchJobStatus.PAUSED);
        batchJobRepository.save(job);
        
        taskScheduler.pauseBatchJob(jobId);
    }

    public void resumeBatchJob(Long jobId) {
        BatchJob job = batchJobRepository.findById(jobId)
            .orElseThrow(() -> new EntityNotFoundException("批量任务不存在"));
        
        job.setStatus(BatchJobStatus.RUNNING);
        batchJobRepository.save(job);
        
        taskScheduler.resumeBatchJob(jobId);
    }
}
```

### 任务调度器
```java
@Service
public class TaskScheduler {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private WorkerPool workerPool;
    
    @Autowired
    private BatchJobRepository batchJobRepository;

    private static final String BATCH_QUEUE_KEY = "batch:queue";
    private static final String PROCESSING_JOBS_KEY = "batch:processing";

    public void submitBatchJob(BatchJob batchJob) {
        // 将任务添加到优先级队列
        redisTemplate.opsForZSet().add(BATCH_QUEUE_KEY, batchJob.getId(), batchJob.getPriority());
        
        log.info("批量任务已提交到队列: {}", batchJob.getId());
        
        // 尝试立即调度
        scheduleNextJob();
    }

    @Scheduled(fixedDelay = 5000) // 每5秒检查一次
    public void scheduleNextJob() {
        // 检查是否有可用的工作线程
        if (!workerPool.hasAvailableWorkers()) {
            return;
        }
        
        // 从队列中获取最高优先级的任务
        Set<Object> jobs = redisTemplate.opsForZSet().reverseRange(BATCH_QUEUE_KEY, 0, 0);
        if (jobs.isEmpty()) {
            return;
        }
        
        Long jobId = (Long) jobs.iterator().next();
        
        // 从队列中移除
        redisTemplate.opsForZSet().remove(BATCH_QUEUE_KEY, jobId);
        
        // 添加到处理中列表
        redisTemplate.opsForSet().add(PROCESSING_JOBS_KEY, jobId);
        
        // 分配工作线程
        workerPool.assignJob(jobId);
        
        log.info("批量任务开始处理: {}", jobId);
    }

    public void pauseBatchJob(Long jobId) {
        // 从队列中移除
        redisTemplate.opsForZSet().remove(BATCH_QUEUE_KEY, jobId);
        
        // 从处理中列表移除
        redisTemplate.opsForSet().remove(PROCESSING_JOBS_KEY, jobId);
        
        // 通知工作线程暂停
        workerPool.pauseJob(jobId);
    }

    public void resumeBatchJob(Long jobId) {
        BatchJob job = batchJobRepository.findById(jobId).orElse(null);
        if (job != null) {
            // 重新添加到队列
            redisTemplate.opsForZSet().add(BATCH_QUEUE_KEY, jobId, job.getPriority());
        }
    }
}
```

### 工作线程池
```java
@Service
public class WorkerPool {

    @Autowired
    private DocumentProcessor documentProcessor;
    
    @Autowired
    private AIExtractionEngine aiExtractionEngine;
    
    @Autowired
    private ProgressMonitor progressMonitor;

    private final Map<Long, BatchJobWorker> activeWorkers = new ConcurrentHashMap<>();
    private final ExecutorService executorService;

    public WorkerPool(@Value("${batch.processing.max-workers-per-job}") int maxWorkers) {
        this.executorService = Executors.newFixedThreadPool(maxWorkers);
    }

    public boolean hasAvailableWorkers() {
        return activeWorkers.size() < getMaxConcurrentJobs();
    }

    public void assignJob(Long jobId) {
        if (activeWorkers.containsKey(jobId)) {
            log.warn("任务已在处理中: {}", jobId);
            return;
        }
        
        BatchJobWorker worker = new BatchJobWorker(jobId, this);
        activeWorkers.put(jobId, worker);
        
        executorService.submit(worker);
    }

    public void pauseJob(Long jobId) {
        BatchJobWorker worker = activeWorkers.get(jobId);
        if (worker != null) {
            worker.pause();
        }
    }

    public void completeJob(Long jobId) {
        activeWorkers.remove(jobId);
        log.info("任务处理完成: {}", jobId);
    }

    @PreDestroy
    public void shutdown() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
        }
    }
}
```

### 批量任务工作线程
```java
public class BatchJobWorker implements Runnable {

    private final Long jobId;
    private final WorkerPool workerPool;
    private volatile boolean paused = false;
    private volatile boolean stopped = false;

    @Autowired
    private DocumentTaskRepository documentTaskRepository;
    
    @Autowired
    private DocumentProcessor documentProcessor;
    
    @Autowired
    private AIExtractionEngine aiExtractionEngine;

    public BatchJobWorker(Long jobId, WorkerPool workerPool) {
        this.jobId = jobId;
        this.workerPool = workerPool;
    }

    @Override
    public void run() {
        try {
            log.info("开始处理批量任务: {}", jobId);
            
            // 更新任务状态
            updateBatchJobStatus(BatchJobStatus.RUNNING);
            
            // 获取待处理的文档任务
            List<DocumentTask> pendingTasks = documentTaskRepository
                .findByBatchJobIdAndStatus(jobId, DocumentTaskStatus.PENDING);
            
            for (DocumentTask task : pendingTasks) {
                if (stopped) {
                    break;
                }
                
                // 检查暂停状态
                while (paused && !stopped) {
                    Thread.sleep(1000);
                }
                
                processDocumentTask(task);
            }
            
            // 完成任务
            updateBatchJobStatus(BatchJobStatus.COMPLETED);
            
        } catch (Exception e) {
            log.error("批量任务处理失败: {}", jobId, e);
            updateBatchJobStatus(BatchJobStatus.FAILED);
        } finally {
            workerPool.completeJob(jobId);
        }
    }

    private void processDocumentTask(DocumentTask task) {
        try {
            task.setStatus(DocumentTaskStatus.PROCESSING);
            task.setStartedAt(LocalDateTime.now());
            documentTaskRepository.save(task);
            
            long startTime = System.currentTimeMillis();
            
            // 1. 处理文档
            ProcessingResult processingResult = documentProcessor.processDocument(task.getDocumentPath());
            
            // 2. AI抽取
            ExtractionRequest extractionRequest = buildExtractionRequest(task, processingResult);
            ExtractionResult extractionResult = aiExtractionEngine.extractData(extractionRequest);
            
            // 3. 保存结果
            task.setExtractionResult(extractionResult.getExtractedData());
            task.setConfidenceScores(extractionResult.getConfidenceScores());
            task.setStatus(DocumentTaskStatus.COMPLETED);
            task.setProcessingTime((int) (System.currentTimeMillis() - startTime));
            task.setCompletedAt(LocalDateTime.now());
            
            documentTaskRepository.save(task);
            
            // 4. 更新批量任务进度
            updateBatchJobProgress();
            
        } catch (Exception e) {
            log.error("文档任务处理失败: {}", task.getId(), e);
            
            task.setStatus(DocumentTaskStatus.FAILED);
            task.setErrorMessage(e.getMessage());
            task.setRetryCount(task.getRetryCount() + 1);
            task.setCompletedAt(LocalDateTime.now());
            
            documentTaskRepository.save(task);
            
            // 如果重试次数未超限，重新加入队列
            if (task.getRetryCount() < getMaxRetryAttempts()) {
                scheduleRetry(task);
            }
        }
    }

    public void pause() {
        this.paused = true;
    }

    public void resume() {
        this.paused = false;
    }

    public void stop() {
        this.stopped = true;
    }
}
```

### 进度监控器
```java
@Service
public class ProgressMonitor {

    @Autowired
    private BatchJobRepository batchJobRepository;
    
    @Autowired
    private DocumentTaskRepository documentTaskRepository;

    @Scheduled(fixedDelay = 5000) // 每5秒更新一次进度
    public void updateProgress() {
        List<BatchJob> runningJobs = batchJobRepository.findByStatus(BatchJobStatus.RUNNING);
        
        for (BatchJob job : runningJobs) {
            updateJobProgress(job);
        }
    }

    private void updateJobProgress(BatchJob job) {
        // 统计任务状态
        Map<DocumentTaskStatus, Long> statusCounts = documentTaskRepository
            .countByBatchJobIdGroupByStatus(job.getId());
        
        int processed = statusCounts.getOrDefault(DocumentTaskStatus.COMPLETED, 0L).intValue() +
                       statusCounts.getOrDefault(DocumentTaskStatus.FAILED, 0L).intValue();
        int success = statusCounts.getOrDefault(DocumentTaskStatus.COMPLETED, 0L).intValue();
        int failed = statusCounts.getOrDefault(DocumentTaskStatus.FAILED, 0L).intValue();
        
        // 更新进度信息
        job.setProcessedDocuments(processed);
        job.setSuccessDocuments(success);
        job.setFailedDocuments(failed);
        
        // 计算预计完成时间
        if (processed > 0) {
            long elapsedTime = Duration.between(job.getStartedAt(), LocalDateTime.now()).toMillis();
            long avgTimePerDoc = elapsedTime / processed;
            long remainingDocs = job.getTotalDocuments() - processed;
            long estimatedRemainingTime = avgTimePerDoc * remainingDocs;
            
            job.setEstimatedCompletion(LocalDateTime.now().plus(estimatedRemainingTime, ChronoUnit.MILLIS));
        }
        
        // 构建详细进度信息
        Map<String, Object> progressInfo = new HashMap<>();
        progressInfo.put("totalDocuments", job.getTotalDocuments());
        progressInfo.put("processedDocuments", processed);
        progressInfo.put("successDocuments", success);
        progressInfo.put("failedDocuments", failed);
        progressInfo.put("progressPercentage", (double) processed / job.getTotalDocuments() * 100);
        progressInfo.put("statusCounts", statusCounts);
        
        job.setProgressInfo(progressInfo);
        
        batchJobRepository.save(job);
    }
}
```

## 🧪 测试用例

### 功能测试
- 批量任务创建和管理测试
- 任务队列调度测试
- 并行处理能力测试
- 错误处理和重试测试
- 暂停和恢复功能测试

### 性能测试
- 大批量文档处理测试
- 并发任务处理测试
- 内存使用优化测试
- 队列性能测试

### 稳定性测试
- 长时间运行稳定性测试
- 异常情况恢复测试
- 资源泄漏检测测试
- 故障转移测试

---

**相关文档**:
- [文档处理引擎](./document-processing-engine.md)
- [AI抽取引擎](./ai-extraction-engine.md)
- [质量控制系统](./quality-control-system.md)
