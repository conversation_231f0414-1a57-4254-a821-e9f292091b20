# AI-FDB v0.4 - 文件处理实现

## 概述

文件处理模块负责处理各种格式的数据文件，包括Excel、CSV、JSON等格式的解析、验证和转换。本模块提供统一的文件处理接口，支持大文件流式处理，确保系统的稳定性和性能。

## 🎯 功能目标

- **多格式支持** - Excel(.xlsx/.xls)、CSV、JSON、TXT等格式
- **流式处理** - 大文件分块读取，减少内存占用
- **编码识别** - 自动识别文件编码格式
- **格式验证** - 文件格式和内容验证
- **错误处理** - 完善的错误检测和恢复机制
- **性能优化** - 高效的文件解析和处理算法

## 🏗️ 系统架构

### 文件处理架构
```
文件上传 → 格式检测 → 编码识别 → 内容解析 → 数据转换 → 结果输出
    ↓        ↓        ↓        ↓        ↓        ↓
  存储管理  格式识别  编码检测  解析引擎  数据转换  结果封装
```

### 核心组件
1. **FileStorageService** - 文件存储服务
2. **FormatDetector** - 格式检测器
3. **EncodingDetector** - 编码检测器
4. **ExcelParser** - Excel解析器
5. **CsvParser** - CSV解析器
6. **JsonParser** - JSON解析器
7. **DataConverter** - 数据转换器

## 🔧 技术实现

### 文件处理配置
```yaml
# application.yml
file:
  upload:
    max-size: 50MB
    allowed-types:
      - application/vnd.ms-excel
      - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      - text/csv
      - application/json
      - text/plain
    temp-dir: ${java.io.tmpdir}/ai-fdb/uploads
    
  storage:
    type: local # 支持 local, minio, s3
    local:
      base-path: ./data/files
    minio:
      endpoint: ${MINIO_ENDPOINT}
      access-key: ${MINIO_ACCESS_KEY}
      secret-key: ${MINIO_SECRET_KEY}
      bucket: ai-fdb-files
      
  processing:
    chunk-size: 1000 # 分块处理大小
    max-rows: 100000 # 最大行数限制
    timeout: 300s # 处理超时时间
```

### 文件存储服务
```java
@Service
public class FileStorageService {

    @Value("${file.storage.type}")
    private String storageType;
    
    @Value("${file.storage.local.base-path}")
    private String localBasePath;

    public String storeFile(MultipartFile file, String category) {
        validateFile(file);
        
        String fileName = generateFileName(file.getOriginalFilename());
        String filePath = buildFilePath(category, fileName);
        
        switch (storageType.toLowerCase()) {
            case "local":
                return storeFileLocally(file, filePath);
            case "minio":
                return storeFileToMinio(file, filePath);
            case "s3":
                return storeFileToS3(file, filePath);
            default:
                throw new IllegalArgumentException("不支持的存储类型: " + storageType);
        }
    }

    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new FileProcessingException("文件不能为空");
        }
        
        if (file.getSize() > getMaxFileSize()) {
            throw new FileProcessingException("文件大小超过限制");
        }
        
        String contentType = file.getContentType();
        if (!isAllowedContentType(contentType)) {
            throw new FileProcessingException("不支持的文件类型: " + contentType);
        }
    }

    private String storeFileLocally(MultipartFile file, String filePath) {
        try {
            Path targetPath = Paths.get(localBasePath, filePath);
            Files.createDirectories(targetPath.getParent());
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);
            return filePath;
        } catch (IOException e) {
            throw new FileProcessingException("文件存储失败", e);
        }
    }
}
```

### Excel解析器
```java
@Component
public class ExcelParser implements FileParser {

    @Override
    public boolean supports(String fileName, String contentType) {
        return fileName.endsWith(".xlsx") || fileName.endsWith(".xls") ||
               "application/vnd.ms-excel".equals(contentType) ||
               "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(contentType);
    }

    @Override
    public ParseResult parse(String filePath, ParseOptions options) {
        try (InputStream inputStream = Files.newInputStream(Paths.get(filePath));
             Workbook workbook = WorkbookFactory.create(inputStream)) {
            
            Sheet sheet = getTargetSheet(workbook, options);
            List<Map<String, Object>> records = new ArrayList<>();
            List<String> headers = extractHeaders(sheet, options);
            
            int startRow = options.getHeaderRow() + 1;
            int maxRows = Math.min(sheet.getLastRowNum() + 1, options.getMaxRows());
            
            for (int rowIndex = startRow; rowIndex < maxRows; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null || isEmptyRow(row)) {
                    continue;
                }
                
                Map<String, Object> record = parseRow(row, headers, options);
                if (!record.isEmpty()) {
                    records.add(record);
                }
            }
            
            return ParseResult.builder()
                .headers(headers)
                .records(records)
                .totalRows(records.size())
                .sheetNames(getSheetNames(workbook))
                .build();
                
        } catch (Exception e) {
            throw new FileProcessingException("Excel文件解析失败", e);
        }
    }

    private List<String> extractHeaders(Sheet sheet, ParseOptions options) {
        Row headerRow = sheet.getRow(options.getHeaderRow());
        if (headerRow == null) {
            throw new FileProcessingException("未找到表头行");
        }
        
        List<String> headers = new ArrayList<>();
        for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
            Cell cell = headerRow.getCell(cellIndex);
            String header = getCellValueAsString(cell);
            headers.add(StringUtils.hasText(header) ? header : "Column" + (cellIndex + 1));
        }
        
        return headers;
    }

    private Map<String, Object> parseRow(Row row, List<String> headers, ParseOptions options) {
        Map<String, Object> record = new HashMap<>();
        
        for (int cellIndex = 0; cellIndex < headers.size() && cellIndex < row.getLastCellNum(); cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            String header = headers.get(cellIndex);
            Object value = getCellValue(cell);
            
            if (value != null) {
                record.put(header, value);
            }
        }
        
        return record;
    }

    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return numericValue;
                    }
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return evaluateFormula(cell);
            default:
                return null;
        }
    }
}
```

### CSV解析器
```java
@Component
public class CsvParser implements FileParser {

    @Override
    public boolean supports(String fileName, String contentType) {
        return fileName.endsWith(".csv") || "text/csv".equals(contentType);
    }

    @Override
    public ParseResult parse(String filePath, ParseOptions options) {
        try {
            // 检测编码
            String encoding = detectEncoding(filePath);
            
            // 检测分隔符
            char delimiter = detectDelimiter(filePath, encoding);
            
            List<Map<String, Object>> records = new ArrayList<>();
            List<String> headers = null;
            
            try (Reader reader = Files.newBufferedReader(Paths.get(filePath), Charset.forName(encoding));
                 CSVParser csvParser = CSVFormat.DEFAULT
                     .withDelimiter(delimiter)
                     .withFirstRecordAsHeader()
                     .withIgnoreEmptyLines(true)
                     .withTrim()
                     .parse(reader)) {
                
                headers = new ArrayList<>(csvParser.getHeaderNames());
                
                for (CSVRecord record : csvParser) {
                    if (records.size() >= options.getMaxRows()) {
                        break;
                    }
                    
                    Map<String, Object> recordMap = new HashMap<>();
                    for (String header : headers) {
                        String value = record.get(header);
                        if (StringUtils.hasText(value)) {
                            recordMap.put(header, convertValue(value));
                        }
                    }
                    
                    if (!recordMap.isEmpty()) {
                        records.add(recordMap);
                    }
                }
            }
            
            return ParseResult.builder()
                .headers(headers)
                .records(records)
                .totalRows(records.size())
                .encoding(encoding)
                .delimiter(String.valueOf(delimiter))
                .build();
                
        } catch (Exception e) {
            throw new FileProcessingException("CSV文件解析失败", e);
        }
    }

    private String detectEncoding(String filePath) {
        try (InputStream inputStream = Files.newInputStream(Paths.get(filePath))) {
            UniversalDetector detector = new UniversalDetector(null);
            
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) > 0 && !detector.isDone()) {
                detector.handleData(buffer, 0, bytesRead);
            }
            detector.dataEnd();
            
            String encoding = detector.getDetectedCharset();
            detector.reset();
            
            return encoding != null ? encoding : "UTF-8";
        } catch (IOException e) {
            return "UTF-8";
        }
    }

    private char detectDelimiter(String filePath, String encoding) {
        char[] candidates = {',', ';', '\t', '|'};
        Map<Character, Integer> counts = new HashMap<>();
        
        try (Reader reader = Files.newBufferedReader(Paths.get(filePath), Charset.forName(encoding))) {
            String firstLine = new BufferedReader(reader).readLine();
            if (firstLine != null) {
                for (char candidate : candidates) {
                    int count = (int) firstLine.chars().filter(ch -> ch == candidate).count();
                    counts.put(candidate, count);
                }
            }
        } catch (IOException e) {
            return ','; // 默认使用逗号
        }
        
        return counts.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(',');
    }

    private Object convertValue(String value) {
        // 尝试转换为数字
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Long.parseLong(value);
            }
        } catch (NumberFormatException e) {
            // 不是数字，返回字符串
        }
        
        // 尝试转换为布尔值
        if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
            return Boolean.parseBoolean(value);
        }
        
        // 尝试转换为日期
        try {
            return parseDate(value);
        } catch (Exception e) {
            // 不是日期，返回字符串
        }
        
        return value;
    }
}
```

### JSON解析器
```java
@Component
public class JsonParser implements FileParser {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean supports(String fileName, String contentType) {
        return fileName.endsWith(".json") || "application/json".equals(contentType);
    }

    @Override
    public ParseResult parse(String filePath, ParseOptions options) {
        try {
            String content = Files.readString(Paths.get(filePath));
            JsonNode rootNode = objectMapper.readTree(content);
            
            List<Map<String, Object>> records = new ArrayList<>();
            Set<String> allHeaders = new LinkedHashSet<>();
            
            if (rootNode.isArray()) {
                // JSON数组格式
                for (JsonNode node : rootNode) {
                    if (records.size() >= options.getMaxRows()) {
                        break;
                    }
                    
                    Map<String, Object> record = convertJsonNodeToMap(node);
                    allHeaders.addAll(record.keySet());
                    records.add(record);
                }
            } else if (rootNode.isObject()) {
                // 单个JSON对象
                Map<String, Object> record = convertJsonNodeToMap(rootNode);
                allHeaders.addAll(record.keySet());
                records.add(record);
            } else {
                throw new FileProcessingException("不支持的JSON格式");
            }
            
            return ParseResult.builder()
                .headers(new ArrayList<>(allHeaders))
                .records(records)
                .totalRows(records.size())
                .build();
                
        } catch (Exception e) {
            throw new FileProcessingException("JSON文件解析失败", e);
        }
    }

    private Map<String, Object> convertJsonNodeToMap(JsonNode node) {
        Map<String, Object> map = new HashMap<>();
        
        node.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (value.isNull()) {
                map.put(key, null);
            } else if (value.isTextual()) {
                map.put(key, value.asText());
            } else if (value.isNumber()) {
                if (value.isInt()) {
                    map.put(key, value.asLong());
                } else {
                    map.put(key, value.asDouble());
                }
            } else if (value.isBoolean()) {
                map.put(key, value.asBoolean());
            } else if (value.isArray() || value.isObject()) {
                // 复杂类型转换为JSON字符串
                map.put(key, value.toString());
            }
        });
        
        return map;
    }
}
```

## 🧪 测试用例

### 功能测试
- 各种文件格式解析测试
- 编码识别准确性测试
- 大文件处理性能测试
- 错误文件处理测试
- 边界情况处理测试

### 性能测试
- 大文件解析性能测试
- 内存使用优化测试
- 并发文件处理测试
- 流式处理效果测试

### 兼容性测试
- 不同Excel版本兼容性
- 各种CSV编码格式测试
- JSON格式兼容性测试
- 操作系统兼容性测试

---

**相关文档**:
- [数据导入设计](./data-import-design.md)
- [数据管理实现](./data-management-implementation.md)
- [AI抽取服务](./ai-extraction-service.md)
