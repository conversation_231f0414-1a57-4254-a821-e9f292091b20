# AI-FDB v0.2 - 项目架构设计

## 架构概述

v0.2版本在继承v0.1用户认证系统的基础上，新增OCR文档识别和AI语义抽取核心模块。整体架构采用微服务设计思想，通过模块化的方式集成PaddleOCR和通义千问，实现高可用、高性能的文档处理系统。

## 🏗️ 总体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        AI-FDB v0.2 系统架构                        │
├─────────────────────────────────────────────────────────────────┤
│                          前端层 (Vue3)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  用户认证   │ │  文档上传   │ │  OCR展示   │ │  抽取配置   │ │
│  │   模块     │ │   模块     │ │   模块     │ │   模块     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        API网关层 (Spring Boot)                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  认证接口   │ │  文档接口   │ │  OCR接口   │ │  AI抽取接口 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                          业务服务层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 用户服务    │ │ 文档服务    │ │ OCR服务     │ │ AI抽取服务  │ │
│  │ (继承v0.1)  │ │ (新增)      │ │ (新增)      │ │ (新增)      │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                          AI引擎层                                │
│  ┌─────────────────────────────┐ ┌─────────────────────────────┐ │
│  │        PaddleOCR引擎        │ │       通义千问引擎          │ │
│  │   PP-OCRv5_server_rec      │ │       qwen-turbo           │ │
│  │   - 文本检测               │ │   - 语义理解               │ │
│  │   - 文本识别               │ │   - 字段抽取               │ │
│  │   - 版面分析               │ │   - 置信度评估             │ │
│  └─────────────────────────────┘ └─────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                          数据存储层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │   MySQL     │ │    Redis    │ │  文件存储   │ │  模型缓存   │ │
│  │ (用户+OCR)  │ │  (会话缓存) │ │ (文档文件)  │ │ (AI模型)    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 技术架构详解

### 继承v0.1架构
- **用户认证系统**: JWT认证、权限管理、会话管理
- **基础框架**: Spring Boot 3.x + Vue 3 + MySQL + Redis
- **安全机制**: BCrypt密码加密、CORS配置、API安全

### v0.2新增架构组件

#### 1. 文档处理层
```
文档处理流程:
上传文件 → 格式检测 → 预处理 → OCR识别 → AI抽取 → 结果存储
```

**支持格式**:
- PDF文档 (使用PDFBox解析)
- Word文档 (使用Apache POI解析)
- Excel文档 (使用Apache POI解析)
- 图片文件 (JPG, PNG, TIFF, BMP)

#### 2. OCR识别层
```
PaddleOCR处理链路:
图像预处理 → 文本检测 → 文本识别 → 结果后处理
```

**核心组件**:
- **文本检测**: PP-OCRv4_server_det
- **文本识别**: PP-OCRv5_server_rec (核心引擎)
- **方向分类**: ch_ppocr_mobile_v2.0_cls
- **版面分析**: 自动版面结构识别

#### 3. AI语义抽取层
```
通义千问处理链路:
文本输入 → 语义理解 → 字段抽取 → 置信度评估 → 结果输出
```

**核心功能**:
- **语义理解**: 基于qwen-turbo的深度语义分析
- **字段抽取**: 用户自定义字段的智能抽取
- **置信度评估**: 抽取结果的可信度评分
- **上下文理解**: 复杂文档的上下文关联分析

## 📊 数据流架构

### 文档处理数据流
```mermaid
graph TD
    A[用户上传文档] --> B[文件格式验证]
    B --> C[文档预处理]
    C --> D[OCR文字识别]
    D --> E[识别结果存储]
    E --> F[AI语义抽取]
    F --> G[抽取结果存储]
    G --> H[结果展示]
    
    D --> I[PaddleOCR引擎]
    F --> J[通义千问引擎]
    
    E --> K[MySQL数据库]
    G --> K
    
    I --> L[模型缓存]
    J --> M[API调用]
```

### 数据存储架构
```
MySQL数据库:
├── 用户相关表 (继承v0.1)
│   ├── users (用户表)
│   ├── user_sessions (会话表)
│   └── system_configs (配置表)
└── OCR和AI相关表 (v0.2新增)
    ├── ocr_results (OCR识别结果)
    ├── semantic_extractions (语义抽取记录)
    ├── extraction_field_configs (字段配置)
    └── document_processing_tasks (处理任务)

Redis缓存:
├── 用户会话缓存 (继承v0.1)
├── OCR结果缓存 (v0.2新增)
├── AI抽取结果缓存 (v0.2新增)
└── 模型配置缓存 (v0.2新增)

文件存储:
├── 上传文档存储
├── OCR处理临时文件
└── 处理结果文件
```

## 🔌 服务集成架构

### PaddleOCR集成架构
```
Java后端 ←→ Python脚本 ←→ PaddleOCR引擎
    ↓           ↓              ↓
配置管理    进程通信        模型推理
    ↓           ↓              ↓
参数传递    结果解析        文字识别
```

**集成方式**:
- **进程调用**: Java通过ProcessBuilder调用Python脚本
- **参数传递**: JSON格式的配置参数传递
- **结果解析**: 标准化的JSON结果返回
- **错误处理**: 完善的异常捕获和重试机制

### 通义千问集成架构
```
Java后端 ←→ Python脚本 ←→ DashScope API
    ↓           ↓              ↓
字段配置    API调用        语义分析
    ↓           ↓              ↓
文本输入    结果解析        字段抽取
```

**集成方式**:
- **API调用**: 通过DashScope SDK调用qwen-turbo
- **提示工程**: 精心设计的提示词模板
- **结果解析**: 结构化的JSON结果处理
- **置信度评估**: 抽取结果的可信度分析

## 🚀 性能优化架构

### 缓存策略
```
多层缓存架构:
├── 应用层缓存 (Spring Cache)
├── Redis分布式缓存
├── OCR模型缓存
└── AI API结果缓存
```

### 异步处理架构
```
异步任务处理:
文档上传 → 任务队列 → 后台处理 → 结果通知
    ↓         ↓         ↓         ↓
即时响应   任务调度   并行处理   实时更新
```

### 负载均衡架构
```
负载均衡策略:
├── 前端负载均衡 (Nginx)
├── 后端服务负载均衡
├── OCR处理负载均衡
└── AI API调用限流
```

## 🔒 安全架构

### 继承v0.1安全机制
- JWT认证和授权
- 密码加密存储
- 会话管理
- API安全防护

### v0.2新增安全机制
```
文档安全处理:
├── 文件类型验证
├── 文件大小限制
├── 恶意文件检测
├── 敏感信息脱敏
└── 处理结果加密
```

### API安全架构
```
API安全防护:
├── 认证鉴权 (JWT)
├── 请求限流 (Rate Limiting)
├── 参数验证 (Validation)
├── 错误处理 (Error Handling)
└── 日志审计 (Audit Logging)
```

## 📈 监控和运维架构

### 系统监控
```
监控体系:
├── 应用性能监控 (APM)
├── 基础设施监控
├── OCR处理监控
├── AI API调用监控
└── 业务指标监控
```

### 日志架构
```
日志管理:
├── 应用日志 (Logback)
├── 访问日志 (Nginx)
├── OCR处理日志
├── AI调用日志
└── 错误日志
```

### 运维架构
```
运维体系:
├── 容器化部署 (Docker)
├── 服务编排 (Docker Compose)
├── 配置管理 (Environment Variables)
├── 健康检查 (Health Check)
└── 自动重启 (Auto Restart)
```

## 🔄 扩展性架构

### 水平扩展
- 前端服务可水平扩展
- 后端API服务可集群部署
- OCR处理可并行扩展
- AI调用可负载均衡

### 垂直扩展
- 数据库性能优化
- Redis缓存扩容
- 文件存储扩展
- 模型推理加速

### 模块化扩展
- OCR引擎可插拔替换
- AI模型可灵活切换
- 文档格式可动态支持
- 抽取字段可自定义扩展

## 📋 架构决策记录

### 技术选型决策
1. **OCR引擎选择**: PaddleOCR PP-OCRv5_server_rec
   - 理由: 开源免费、精度高、支持中文、社区活跃
   
2. **AI模型选择**: 通义千问 qwen-turbo
   - 理由: 中文理解能力强、API稳定、成本可控
   
3. **集成方式选择**: Python脚本 + 进程调用
   - 理由: 技术栈隔离、易于维护、性能可接受

### 架构模式决策
1. **微服务架构**: 模块化设计，便于独立开发和部署
2. **事件驱动**: 异步处理，提升系统响应性能
3. **缓存优先**: 多层缓存，减少重复计算和API调用

### 数据架构决策
1. **关系型数据库**: MySQL存储结构化数据
2. **NoSQL缓存**: Redis存储临时数据和缓存
3. **文件存储**: 本地文件系统存储文档文件

这个架构设计确保了v0.2版本在继承v0.1稳定基础的同时，能够高效、安全、可扩展地实现OCR和AI核心功能。
