# AI-FDB v0.3 - 数据库设计

## 版本说明

v0.3版本在v0.2基础上新增工作空间管理和数据表设计相关的数据库表结构。

## 继承v0.2数据库结构

v0.3版本完全继承v0.2版本的所有数据库表：
- 用户认证相关表（users、user_sessions等）
- OCR处理相关表（ocr_tasks、ocr_results等）
- AI语义抽取相关表（extraction_tasks、extraction_results等）

详细信息请参考：
- [v0.1数据库设计](../v0.1/database-design.md) - 用户认证相关表
- [v0.2数据库设计](../v0.2/database-design.md) - OCR和AI相关表

## v0.3新增表结构

### 1. 工作空间表 (workspaces)

工作空间是数据表的容器，每个工作空间可以包含多个数据表。

```sql
CREATE TABLE workspaces (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id BIGINT NOT NULL,
    settings JSON COMMENT '工作空间设置',
    status ENUM('active', 'archived') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id),
    INDEX idx_owner_id (owner_id),
    INDEX idx_status (status)
);
```

### 2. 工作空间成员表 (workspace_members)

```sql
CREATE TABLE workspace_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role ENUM('owner', 'admin', 'editor', 'viewer') NOT NULL,
    permissions JSON COMMENT '详细权限配置',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_workspace_user (workspace_id, user_id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_user_id (user_id)
);
```

### 3. 数据表定义表 (data_tables)

```sql
CREATE TABLE data_tables (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    description TEXT,
    workspace_id BIGINT NOT NULL,
    table_schema JSON NOT NULL COMMENT '表结构定义',
    ai_config JSON COMMENT 'AI抽取配置',
    version INT DEFAULT 1,
    status ENUM('draft', 'active', 'archived') DEFAULT 'draft',
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_name (name),
    INDEX idx_status (status),
    UNIQUE KEY uk_workspace_name (workspace_id, name)
);
```

### 4. 字段定义表 (table_fields)

```sql
CREATE TABLE table_fields (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    field_type ENUM('text', 'number', 'date', 'datetime', 'boolean', 'file', 'url', 'email', 'phone', 'select', 'multiselect') NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    is_unique BOOLEAN DEFAULT FALSE,
    field_order INT DEFAULT 0,
    default_value TEXT,
    field_options JSON COMMENT '字段选项配置',
    validation_rules JSON COMMENT '字段验证规则',
    extraction_prompt TEXT COMMENT 'AI抽取提示词',
    extraction_examples JSON COMMENT '抽取示例',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    UNIQUE KEY uk_table_field (table_id, field_name),
    INDEX idx_table_id (table_id),
    INDEX idx_field_order (field_order)
);
```

### 5. 表模板表 (table_templates)

```sql
CREATE TABLE table_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    description TEXT,
    template_schema JSON NOT NULL,
    usage_count INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_public (is_public),
    INDEX idx_usage_count (usage_count)
);
```

### 6. AI生成历史表 (ai_generation_history)

```sql
CREATE TABLE ai_generation_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT,
    generation_type ENUM('table_structure', 'field_suggestion', 'prompt_generation') NOT NULL,
    input_prompt TEXT NOT NULL,
    generated_result JSON NOT NULL,
    confidence_score DECIMAL(3,2),
    user_feedback ENUM('accepted', 'modified', 'rejected'),
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_generation_type (generation_type),
    INDEX idx_created_at (created_at)
);
```

## 字段类型系统

### 基础字段类型配置

#### 文本类型 (text)
```json
{
  "type": "text",
  "maxLength": 255,
  "minLength": 0,
  "pattern": "regex_pattern",
  "placeholder": "请输入文本"
}
```

#### 数字类型 (number)
```json
{
  "type": "number",
  "min": 0,
  "max": 999999,
  "precision": 2,
  "unit": "元"
}
```

#### 日期类型 (date/datetime)
```json
{
  "type": "date",
  "format": "YYYY-MM-DD",
  "minDate": "2020-01-01",
  "maxDate": "2030-12-31"
}
```

#### 选择类型 (select/multiselect)
```json
{
  "type": "select",
  "options": [
    {"value": "option1", "label": "选项1"},
    {"value": "option2", "label": "选项2"}
  ],
  "allowCustom": false
}
```

#### 文件类型 (file)
```json
{
  "type": "file",
  "allowedTypes": ["pdf", "doc", "docx", "jpg", "png"],
  "maxSize": 10485760,
  "multiple": false
}
```

### 验证规则系统

```json
{
  "required": true,
  "unique": false,
  "custom": [
    {
      "rule": "email",
      "message": "请输入有效的邮箱地址"
    },
    {
      "rule": "phone",
      "message": "请输入有效的手机号码"
    }
  ]
}
```

## 数据库初始化脚本

### 创建v0.3新增表

```sql
-- 在v0.2基础上添加工作空间和数据表相关表

-- 工作空间表
CREATE TABLE workspaces (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id BIGINT NOT NULL,
    settings JSON COMMENT '工作空间设置',
    status ENUM('active', 'archived') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id),
    INDEX idx_owner_id (owner_id),
    INDEX idx_status (status)
);

-- 工作空间成员表
CREATE TABLE workspace_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role ENUM('owner', 'admin', 'editor', 'viewer') NOT NULL,
    permissions JSON COMMENT '详细权限配置',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_workspace_user (workspace_id, user_id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_user_id (user_id)
);

-- 数据表定义表
CREATE TABLE data_tables (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    description TEXT,
    workspace_id BIGINT NOT NULL,
    table_schema JSON NOT NULL COMMENT '表结构定义',
    ai_config JSON COMMENT 'AI抽取配置',
    version INT DEFAULT 1,
    status ENUM('draft', 'active', 'archived') DEFAULT 'draft',
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_name (name),
    INDEX idx_status (status),
    UNIQUE KEY uk_workspace_name (workspace_id, name)
);

-- 字段定义表
CREATE TABLE table_fields (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    field_type ENUM('text', 'number', 'date', 'datetime', 'boolean', 'file', 'url', 'email', 'phone', 'select', 'multiselect') NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    is_unique BOOLEAN DEFAULT FALSE,
    field_order INT DEFAULT 0,
    default_value TEXT,
    field_options JSON COMMENT '字段选项配置',
    validation_rules JSON COMMENT '字段验证规则',
    extraction_prompt TEXT COMMENT 'AI抽取提示词',
    extraction_examples JSON COMMENT '抽取示例',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    UNIQUE KEY uk_table_field (table_id, field_name),
    INDEX idx_table_id (table_id),
    INDEX idx_field_order (field_order)
);

-- 表模板表
CREATE TABLE table_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    description TEXT,
    template_schema JSON NOT NULL,
    usage_count INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_public (is_public),
    INDEX idx_usage_count (usage_count)
);

-- AI生成历史表
CREATE TABLE ai_generation_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT,
    generation_type ENUM('table_structure', 'field_suggestion', 'prompt_generation') NOT NULL,
    input_prompt TEXT NOT NULL,
    generated_result JSON NOT NULL,
    confidence_score DECIMAL(3,2),
    user_feedback ENUM('accepted', 'modified', 'rejected'),
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_generation_type (generation_type),
    INDEX idx_created_at (created_at)
);
```

## 索引优化建议

1. **工作空间查询优化**
   - 按用户ID查询工作空间：`idx_owner_id`
   - 按状态过滤：`idx_status`

2. **数据表查询优化**
   - 按工作空间查询表：`idx_workspace_id`
   - 按表名搜索：`idx_name`
   - 按状态过滤：`idx_status`

3. **字段查询优化**
   - 按表ID查询字段：`idx_table_id`
   - 按字段顺序排序：`idx_field_order`

4. **AI历史查询优化**
   - 按表ID查询历史：`idx_table_id`
   - 按生成类型过滤：`idx_generation_type`
   - 按时间排序：`idx_created_at`
