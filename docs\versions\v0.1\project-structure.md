# AI-FDB v0.1 - 项目结构设计

## 项目总体结构

```
AI-FDB/
├── backend/                    # 后端Spring Boot项目
│   ├── src/main/java/com/aifdb/
│   │   ├── config/            # 配置类
│   │   │   ├── SecurityConfig.java
│   │   │   ├── DatabaseConfig.java
│   │   │   ├── RedisConfig.java
│   │   │   └── CorsConfig.java
│   │   ├── controller/        # 控制器层
│   │   │   ├── AuthController.java
│   │   │   └── UserController.java
│   │   ├── service/           # 服务层
│   │   │   ├── UserService.java
│   │   │   ├── AuthService.java
│   │   │   └── JwtService.java
│   │   ├── repository/        # 数据访问层
│   │   │   ├── UserRepository.java
│   │   │   └── UserSessionRepository.java
│   │   ├── entity/            # 实体类
│   │   │   ├── User.java
│   │   │   └── UserSession.java
│   │   ├── dto/               # 数据传输对象
│   │   │   ├── request/
│   │   │   │   ├── LoginRequest.java
│   │   │   │   ├── RegisterRequest.java
│   │   │   │   └── RefreshTokenRequest.java
│   │   │   └── response/
│   │   │       ├── AuthResponse.java
│   │   │       ├── UserResponse.java
│   │   │       └── ApiResponse.java
│   │   ├── security/          # 安全相关
│   │   │   ├── JwtAuthenticationFilter.java
│   │   │   ├── JwtTokenProvider.java
│   │   │   └── CustomUserDetailsService.java
│   │   ├── exception/         # 异常处理
│   │   │   ├── GlobalExceptionHandler.java
│   │   │   └── CustomExceptions.java
│   │   ├── utils/             # 工具类
│   │   │   ├── PasswordUtils.java
│   │   │   ├── ValidationUtils.java
│   │   │   └── DateUtils.java
│   │   └── AiFdbApplication.java
│   ├── src/main/resources/
│   │   ├── application.yml
│   │   ├── application-dev.yml
│   │   ├── application-prod.yml
│   │   └── static/
│   ├── src/test/java/
│   │   ├── controller/
│   │   ├── service/
│   │   └── integration/
│   ├── pom.xml
│   └── Dockerfile
├── frontend/                   # 前端Vue3项目
│   ├── src/
│   │   ├── assets/            # 静态资源
│   │   │   ├── images/
│   │   │   ├── icons/
│   │   │   └── styles/
│   │   │       ├── main.css
│   │   │       ├── variables.css
│   │   │       └── components.css
│   │   ├── components/        # 公共组件
│   │   │   ├── common/
│   │   │   │   ├── Header.vue
│   │   │   │   ├── Footer.vue
│   │   │   │   ├── Loading.vue
│   │   │   │   └── ErrorMessage.vue
│   │   │   └── forms/
│   │   │       ├── LoginForm.vue
│   │   │       ├── RegisterForm.vue
│   │   │       └── UserProfileForm.vue
│   │   ├── views/             # 页面组件
│   │   │   ├── auth/
│   │   │   │   ├── Login.vue
│   │   │   │   └── Register.vue
│   │   │   ├── dashboard/
│   │   │   │   └── Dashboard.vue
│   │   │   └── user/
│   │   │       └── Profile.vue
│   │   ├── stores/            # 状态管理
│   │   │   ├── auth.ts
│   │   │   ├── user.ts
│   │   │   └── app.ts
│   │   ├── router/            # 路由配置
│   │   │   └── index.ts
│   │   ├── api/               # API接口
│   │   │   ├── auth.ts
│   │   │   ├── user.ts
│   │   │   └── request.ts
│   │   ├── utils/             # 工具函数
│   │   │   ├── auth.ts
│   │   │   ├── validation.ts
│   │   │   ├── format.ts
│   │   │   └── constants.ts
│   │   ├── types/             # TypeScript类型定义
│   │   │   ├── auth.ts
│   │   │   ├── user.ts
│   │   │   └── common.ts
│   │   ├── composables/       # 组合式函数
│   │   │   ├── useAuth.ts
│   │   │   └── useUser.ts
│   │   ├── App.vue
│   │   └── main.ts
│   ├── public/
│   │   ├── favicon.ico
│   │   └── index.html
│   ├── package.json
│   ├── vite.config.ts
│   ├── tsconfig.json
│   └── Dockerfile
├── scripts/                    # 脚本文件
│   ├── init-db.sql            # 数据库初始化脚本
│   ├── build.sh               # 构建脚本
│   ├── deploy.sh              # 部署脚本
│   └── docker/
│       ├── docker-compose.yml
│       └── nginx.conf
├── docs/                       # 项目文档
│   ├── versions/v0.1/
│   │   ├── README.md
│   │   ├── project-structure.md
│   │   ├── database-design.md
│   │   ├── backend-implementation.md
│   │   └── frontend-implementation.md
│   ├── project_rules.md
│   ├── backend-api.md
│   └── database-design.md
├── .gitignore
├── README.md
└── docker-compose.yml
```

## 核心模块说明

### 后端模块架构

#### 1. 配置层 (config/)
- **SecurityConfig.java**: Spring Security配置，JWT认证配置
- **DatabaseConfig.java**: 数据库连接配置，JPA配置
- **RedisConfig.java**: Redis缓存配置，会话存储配置
- **CorsConfig.java**: 跨域请求配置

#### 2. 控制器层 (controller/)
- **AuthController.java**: 认证相关接口（登录、注册、退出、刷新token）
- **UserController.java**: 用户信息管理接口（查看、编辑个人信息）

#### 3. 服务层 (service/)
- **UserService.java**: 用户业务逻辑处理
- **AuthService.java**: 认证业务逻辑处理
- **JwtService.java**: JWT令牌生成和验证

#### 4. 数据访问层 (repository/)
- **UserRepository.java**: 用户数据访问接口
- **UserSessionRepository.java**: 用户会话数据访问接口

#### 5. 实体层 (entity/)
- **User.java**: 用户实体类
- **UserSession.java**: 用户会话实体类

### 前端模块架构

#### 1. 页面组件 (views/)
- **auth/**: 认证相关页面（登录、注册）
- **dashboard/**: 仪表板页面
- **user/**: 用户信息管理页面

#### 2. 公共组件 (components/)
- **common/**: 通用组件（头部、底部、加载、错误提示）
- **forms/**: 表单组件（登录表单、注册表单、用户信息表单）

#### 3. 状态管理 (stores/)
- **auth.ts**: 认证状态管理
- **user.ts**: 用户信息状态管理
- **app.ts**: 应用全局状态管理

#### 4. API接口 (api/)
- **auth.ts**: 认证相关API调用
- **user.ts**: 用户相关API调用
- **request.ts**: HTTP请求配置和拦截器

## 技术栈选择

### 后端技术栈
- **Spring Boot 3.x**: 主框架
- **Spring Security**: 安全框架
- **Spring Data JPA**: 数据访问
- **MySQL 8.0**: 主数据库
- **Redis**: 缓存和会话存储
- **JWT**: 认证令牌
- **BCrypt**: 密码加密
- **Maven**: 依赖管理

### 前端技术栈
- **Vue 3**: 前端框架
- **TypeScript**: 类型安全
- **Vite**: 构建工具
- **Element Plus**: UI组件库
- **Pinia**: 状态管理
- **Vue Router 4**: 路由管理
- **Axios**: HTTP客户端

### 开发工具
- **Docker**: 容器化部署
- **Docker Compose**: 多容器编排
- **Git**: 版本控制
- **ESLint**: 代码规范
- **Prettier**: 代码格式化

## 开发环境要求

### 基础环境
- **JDK 17+**: Java开发环境
- **Node.js 18+**: 前端开发环境
- **MySQL 8.0**: 数据库
- **Redis 6.0+**: 缓存服务
- **Git**: 版本控制

### 开发工具推荐
- **IntelliJ IDEA**: Java开发IDE
- **VS Code**: 前端开发编辑器
- **Postman**: API测试工具
- **MySQL Workbench**: 数据库管理工具
- **Redis Desktop Manager**: Redis管理工具

## 部署架构

### 开发环境
```
开发机
├── 前端开发服务器 (Vite Dev Server) :3000
├── 后端开发服务器 (Spring Boot) :8080
├── MySQL数据库 :3306
└── Redis缓存 :6379
```

### 生产环境
```
生产服务器
├── Nginx (反向代理) :80/443
├── 前端静态文件服务
├── 后端应用服务 :8080
├── MySQL数据库 :3306
└── Redis缓存 :6379
```

## 安全设计

### 认证机制
- **JWT令牌认证**: 无状态认证方式
- **刷新令牌机制**: 自动续期，提升用户体验
- **密码加密**: BCrypt加密存储
- **会话管理**: Redis存储会话信息

### 安全防护
- **CORS配置**: 跨域请求安全控制
- **SQL注入防护**: JPA参数化查询
- **XSS防护**: 前端输入验证和转义
- **CSRF防护**: Token验证机制

## 性能优化

### 后端优化
- **数据库连接池**: HikariCP连接池
- **Redis缓存**: 热点数据缓存
- **异步处理**: 非阻塞IO操作
- **数据库索引**: 查询性能优化

### 前端优化
- **代码分割**: 路由级别的懒加载
- **资源压缩**: Vite构建优化
- **缓存策略**: HTTP缓存和浏览器缓存
- **CDN加速**: 静态资源CDN分发

## 监控和日志

### 日志管理
- **Logback**: 后端日志框架
- **日志分级**: DEBUG、INFO、WARN、ERROR
- **日志轮转**: 按大小和时间轮转
- **敏感信息脱敏**: 密码等敏感信息不记录

### 监控指标
- **应用性能**: 响应时间、吞吐量
- **系统资源**: CPU、内存、磁盘使用率
- **数据库性能**: 连接数、查询时间
- **错误监控**: 异常统计和告警
