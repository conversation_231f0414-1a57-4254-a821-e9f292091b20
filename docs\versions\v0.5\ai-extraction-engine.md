# AI-FDB v0.5 - AI抽取引擎

## 概述

AI抽取引擎是v0.5版本的智能核心，负责从文档内容中智能抽取结构化数据。集成多种大语言模型，包括OpenAI GPT、百度文心一言、阿里通义千问等，通过智能提示词和语义理解实现高质量的数据抽取。

## 🎯 功能目标

- **多模型支持** - 集成主流大语言模型服务
- **智能抽取** - 基于语义理解的结构化数据抽取
- **提示词优化** - 可配置的抽取提示词模板
- **结果优化** - 多轮对话和结果精炼
- **置信度评估** - 抽取结果的可信度评分
- **学习优化** - 基于反馈的持续学习

## 🏗️ 系统架构

### AI抽取流程
```
文档内容 → 预处理 → 提示词构建 → AI模型调用 → 结果解析 → 后处理 → 质量评估
    ↓        ↓        ↓         ↓         ↓        ↓        ↓
  内容清理  格式标准化  动态提示词   多模型服务   结构化解析  数据清洗  置信度评分
```

### 核心组件
1. **AIExtractionEngine** - AI抽取主引擎
2. **ModelManager** - AI模型管理器
3. **PromptBuilder** - 提示词构建器
4. **ResultParser** - 结果解析器
5. **ConfidenceEvaluator** - 置信度评估器
6. **LearningOptimizer** - 学习优化器

## 🤖 支持的AI模型

### OpenAI GPT系列
- **GPT-4** - 最强语义理解能力
- **GPT-3.5-turbo** - 性价比最优选择
- **GPT-4-turbo** - 长文本处理能力

### 百度文心系列
- **ERNIE-Bot** - 中文理解优势
- **ERNIE-Bot-turbo** - 快速响应版本
- **ERNIE-Bot-4** - 最新版本

### 阿里通义系列
- **qwen-turbo** - 通用抽取模型
- **qwen-plus** - 增强版本
- **qwen-max** - 最强版本

### 自定义模型
- **本地部署模型** - 私有化部署支持
- **微调模型** - 针对特定领域的微调模型

## 🔧 技术实现

### AI服务配置
```yaml
# application.yml
ai:
  extraction:
    default-model: qwen-turbo
    timeout: 60s
    max-tokens: 4000
    temperature: 0.1
    
  models:
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: https://api.openai.com/v1
      models:
        - gpt-4
        - gpt-3.5-turbo
        - gpt-4-turbo
        
    baidu:
      api-key: ${BAIDU_API_KEY}
      secret-key: ${BAIDU_SECRET_KEY}
      base-url: https://aip.baidubce.com
      models:
        - ernie-bot
        - ernie-bot-turbo
        - ernie-bot-4
        
    alibaba:
      api-key: ${ALIBABA_API_KEY}
      base-url: https://dashscope.aliyuncs.com/api/v1
      models:
        - qwen-turbo
        - qwen-plus
        - qwen-max
        
  prompt:
    templates-dir: classpath:prompts
    enable-dynamic-generation: true
    max-prompt-length: 8000
```

### AI抽取引擎实现
```java
@Service
public class AIExtractionEngine {

    @Autowired
    private ModelManager modelManager;
    
    @Autowired
    private PromptBuilder promptBuilder;
    
    @Autowired
    private ResultParser resultParser;
    
    @Autowired
    private ConfidenceEvaluator confidenceEvaluator;

    public ExtractionResult extractData(ExtractionRequest request) {
        try {
            // 1. 选择最佳模型
            AIModel model = modelManager.selectBestModel(request);
            
            // 2. 构建提示词
            String prompt = promptBuilder.buildPrompt(request);
            
            // 3. 调用AI模型
            AIResponse response = model.chat(prompt);
            
            // 4. 解析结果
            Map<String, Object> extractedData = resultParser.parse(response.getContent());
            
            // 5. 后处理优化
            extractedData = postProcess(extractedData, request.getTableSchema());
            
            // 6. 评估置信度
            Map<String, Double> confidenceScores = confidenceEvaluator.evaluate(
                request.getContent(), extractedData, response);
            
            // 7. 构建结果
            return ExtractionResult.builder()
                .extractedData(extractedData)
                .confidenceScores(confidenceScores)
                .modelUsed(model.getName())
                .promptUsed(prompt)
                .processingTime(System.currentTimeMillis() - startTime)
                .build();
                
        } catch (Exception e) {
            log.error("AI抽取失败", e);
            throw new AIExtractionException("AI抽取失败", e);
        }
    }

    private Map<String, Object> postProcess(Map<String, Object> data, TableSchema schema) {
        Map<String, Object> processedData = new HashMap<>();
        
        for (TableField field : schema.getFields()) {
            String fieldName = field.getName();
            Object rawValue = data.get(fieldName);
            
            if (rawValue != null) {
                Object processedValue = processFieldValue(rawValue, field);
                if (processedValue != null) {
                    processedData.put(fieldName, processedValue);
                }
            }
        }
        
        return processedData;
    }
}
```

### 模型管理器
```java
@Service
public class ModelManager {

    @Autowired
    private List<AIModelProvider> modelProviders;
    
    @Autowired
    private ModelPerformanceTracker performanceTracker;

    public AIModel selectBestModel(ExtractionRequest request) {
        // 根据文档类型、内容长度、历史性能等因素选择最佳模型
        
        // 1. 过滤可用模型
        List<AIModel> availableModels = getAvailableModels();
        
        // 2. 根据内容长度过滤
        availableModels = filterByContentLength(availableModels, request.getContentLength());
        
        // 3. 根据文档类型选择
        availableModels = filterByDocumentType(availableModels, request.getDocumentType());
        
        // 4. 根据历史性能排序
        availableModels.sort((m1, m2) -> {
            double score1 = performanceTracker.getPerformanceScore(m1, request.getDocumentType());
            double score2 = performanceTracker.getPerformanceScore(m2, request.getDocumentType());
            return Double.compare(score2, score1); // 降序排列
        });
        
        // 5. 返回最佳模型
        return availableModels.isEmpty() ? getDefaultModel() : availableModels.get(0);
    }

    private List<AIModel> filterByContentLength(List<AIModel> models, int contentLength) {
        return models.stream()
            .filter(model -> model.getMaxTokens() >= estimateTokens(contentLength))
            .collect(Collectors.toList());
    }
}
```

### 提示词构建器
```java
@Service
public class PromptBuilder {

    @Autowired
    private PromptTemplateManager templateManager;

    public String buildPrompt(ExtractionRequest request) {
        // 1. 获取基础模板
        PromptTemplate template = templateManager.getTemplate(
            request.getDocumentType(), request.getExtractionType());
        
        // 2. 构建字段描述
        String fieldDescriptions = buildFieldDescriptions(request.getTableSchema());
        
        // 3. 构建示例
        String examples = buildExamples(request.getTableSchema(), request.getDocumentType());
        
        // 4. 组装提示词
        return template.render(Map.of(
            "content", request.getContent(),
            "fields", fieldDescriptions,
            "examples", examples,
            "outputFormat", buildOutputFormat(request.getTableSchema())
        ));
    }

    private String buildFieldDescriptions(TableSchema schema) {
        StringBuilder sb = new StringBuilder();
        sb.append("请从文档中抽取以下字段：\n\n");
        
        for (TableField field : schema.getFields()) {
            sb.append("- ").append(field.getDisplayName())
              .append("（").append(field.getName()).append("）")
              .append("：").append(field.getDescription());
            
            if (field.isRequired()) {
                sb.append("【必填】");
            }
            
            sb.append("\n");
        }
        
        return sb.toString();
    }

    private String buildOutputFormat(TableSchema schema) {
        StringBuilder sb = new StringBuilder();
        sb.append("请以JSON格式返回抽取结果：\n");
        sb.append("{\n");
        
        for (TableField field : schema.getFields()) {
            sb.append("  \"").append(field.getName()).append("\": \"抽取的值\",\n");
        }
        
        sb.append("}\n");
        return sb.toString();
    }
}
```

### 通义千问模型实现
```java
@Component
public class QwenModelProvider implements AIModelProvider {

    @Value("${ai.models.alibaba.api-key}")
    private String apiKey;
    
    @Value("${ai.models.alibaba.base-url}")
    private String baseUrl;

    @Override
    public List<AIModel> getAvailableModels() {
        return Arrays.asList(
            new QwenModel("qwen-turbo", 8000, this),
            new QwenModel("qwen-plus", 32000, this),
            new QwenModel("qwen-max", 128000, this)
        );
    }

    public AIResponse chat(String model, String prompt) {
        try {
            // 构建请求
            QwenChatRequest request = QwenChatRequest.builder()
                .model(model)
                .messages(List.of(
                    QwenMessage.builder()
                        .role("user")
                        .content(prompt)
                        .build()
                ))
                .temperature(0.1)
                .maxTokens(4000)
                .build();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);
            
            HttpEntity<QwenChatRequest> entity = new HttpEntity<>(request, headers);
            
            // 发送请求
            ResponseEntity<QwenChatResponse> response = restTemplate.postForEntity(
                baseUrl + "/chat/completions", entity, QwenChatResponse.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                QwenChatResponse chatResponse = response.getBody();
                String content = chatResponse.getChoices().get(0).getMessage().getContent();
                
                return AIResponse.builder()
                    .content(content)
                    .model(model)
                    .usage(chatResponse.getUsage())
                    .timestamp(System.currentTimeMillis())
                    .build();
            } else {
                throw new AIModelException("通义千问调用失败: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("通义千问API调用失败", e);
            throw new AIModelException("通义千问调用失败", e);
        }
    }
}
```

### 置信度评估器
```java
@Service
public class ConfidenceEvaluator {

    public Map<String, Double> evaluate(String sourceContent, 
                                       Map<String, Object> extractedData, 
                                       AIResponse aiResponse) {
        Map<String, Double> confidenceScores = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : extractedData.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();
            
            double confidence = calculateFieldConfidence(fieldName, value, sourceContent, aiResponse);
            confidenceScores.put(fieldName, confidence);
        }
        
        return confidenceScores;
    }

    private double calculateFieldConfidence(String fieldName, Object value, 
                                          String sourceContent, AIResponse aiResponse) {
        double confidence = 0.5; // 基础置信度
        
        // 1. 基于AI模型的内在置信度
        if (aiResponse.getUsage() != null) {
            confidence += 0.2; // AI响应正常
        }
        
        // 2. 基于值的合理性
        if (value != null && !value.toString().trim().isEmpty()) {
            confidence += 0.2; // 有有效值
        }
        
        // 3. 基于源文档中的匹配度
        String valueStr = value != null ? value.toString() : "";
        if (sourceContent.contains(valueStr)) {
            confidence += 0.2; // 在源文档中找到匹配
        }
        
        // 4. 基于字段类型的验证
        confidence += validateFieldType(fieldName, value) ? 0.1 : -0.1;
        
        return Math.max(0.0, Math.min(1.0, confidence));
    }
}
```

## 🎯 提示词模板

### 通用抽取模板
```
你是一个专业的文档数据抽取专家。请从以下文档内容中准确抽取指定的字段信息。

文档内容：
{{content}}

需要抽取的字段：
{{fields}}

抽取要求：
1. 请仔细阅读文档内容，准确理解每个字段的含义
2. 如果某个字段在文档中找不到对应信息，请设置为null
3. 请确保抽取的数据类型与字段定义匹配
4. 日期格式请使用YYYY-MM-DD
5. 数字请使用标准格式，不要包含单位符号

输出格式：
{{outputFormat}}

请开始抽取：
```

### 发票抽取模板
```
你是一个专业的发票信息抽取专家。请从以下发票内容中抽取关键信息。

发票内容：
{{content}}

请抽取以下信息：
- 发票号码：发票的唯一编号
- 开票日期：发票开具日期
- 销售方名称：开票方的公司名称
- 购买方名称：收票方的公司名称
- 金额合计：发票总金额（不含税）
- 税额合计：税额总计
- 价税合计：含税总金额

输出格式：
{
  "invoiceNumber": "发票号码",
  "issueDate": "开票日期",
  "sellerName": "销售方名称",
  "buyerName": "购买方名称",
  "totalAmount": "金额合计",
  "totalTax": "税额合计",
  "totalWithTax": "价税合计"
}
```

## 🧪 测试用例

### 功能测试
- 多种文档类型的抽取测试
- 不同AI模型的对比测试
- 提示词模板效果测试
- 置信度评估准确性测试
- 多轮对话优化测试

### 性能测试
- AI模型响应时间测试
- 大文档抽取性能测试
- 并发抽取压力测试
- 模型切换效率测试

### 准确性测试
- 抽取准确率统计
- 不同领域文档测试
- 复杂格式文档测试
- 边界情况处理测试

---

**相关文档**:
- [文档处理引擎](./document-processing-engine.md)
- [批量处理系统](./batch-processing-system.md)
- [质量控制系统](./quality-control-system.md)
