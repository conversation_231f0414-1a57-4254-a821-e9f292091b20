# AI-FDB v0.5 - 前端抽取组件

## 概述

前端抽取组件模块提供完整的文档抽取用户界面，包括文档上传、抽取任务管理、进度监控、结果审核等核心功能。所有组件基于Vue 3和Element Plus构建，提供直观的操作界面和流畅的用户体验。

## 🎯 设计目标

- **直观易用** - 简化的文档抽取操作流程
- **实时监控** - 抽取任务的实时进度监控
- **结果管理** - 抽取结果的查看、编辑和导出
- **质量控制** - 质量评估和人工审核界面
- **批量处理** - 大批量文档的高效处理界面
- **响应式设计** - 适配各种设备和屏幕尺寸

## 🏗️ 组件架构

### 组件层次结构
```
ExtractionWorkspace (抽取工作空间)
├── DocumentUploader (文档上传器)
├── ExtractionJobManager (抽取任务管理器)
│   ├── JobCreator (任务创建器)
│   ├── JobList (任务列表)
│   ├── JobMonitor (任务监控器)
│   └── JobResults (任务结果)
├── DocumentProcessor (文档处理器)
│   ├── ProcessingQueue (处理队列)
│   ├── ProgressTracker (进度跟踪器)
│   └── ErrorHandler (错误处理器)
├── QualityController (质量控制器)
│   ├── QualityAssessment (质量评估)
│   ├── HumanReview (人工审核)
│   └── FeedbackCollector (反馈收集器)
└── ResultViewer (结果查看器)
    ├── DataGrid (数据网格)
    ├── ExportManager (导出管理器)
    └── HistoryViewer (历史查看器)
```

## 🎨 核心组件

### 1. DocumentUploader - 文档上传器
支持多种文档格式的批量上传组件。

```vue
<template>
  <div class="document-uploader">
    <el-card header="文档上传">
      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :data="uploadData"
          :file-list="fileList"
          :auto-upload="false"
          :accept="acceptedFormats"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          multiple
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文档拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 PDF、Word、Excel、图片等格式，单个文件不超过50MB
            </div>
          </template>
        </el-upload>
      </div>
      
      <!-- 文件列表 -->
      <div class="file-list" v-if="fileList.length > 0">
        <h4>待处理文件 ({{ fileList.length }})</h4>
        <el-table :data="fileList" style="width: 100%">
          <el-table-column prop="name" label="文件名" />
          <el-table-column prop="size" label="大小" :formatter="formatFileSize" />
          <el-table-column prop="type" label="类型" />
          <el-table-column label="状态">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row, $index }">
              <el-button @click="previewFile(row)" size="small" type="text">
                预览
              </el-button>
              <el-button @click="removeFile($index)" size="small" type="text">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 抽取配置 -->
      <div class="extraction-config" v-if="fileList.length > 0">
        <h4>抽取配置</h4>
        <el-form :model="extractionConfig" label-width="120px">
          <el-form-item label="目标数据表">
            <el-select v-model="extractionConfig.tableId" placeholder="选择数据表">
              <el-option
                v-for="table in availableTables"
                :key="table.id"
                :label="table.displayName"
                :value="table.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="抽取模板">
            <el-select v-model="extractionConfig.templateId" placeholder="选择模板">
              <el-option
                v-for="template in extractionTemplates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="AI模型">
            <el-select v-model="extractionConfig.aiModel" placeholder="选择AI模型">
              <el-option label="通义千问-turbo" value="qwen-turbo" />
              <el-option label="通义千问-plus" value="qwen-plus" />
              <el-option label="GPT-3.5-turbo" value="gpt-3.5-turbo" />
              <el-option label="GPT-4" value="gpt-4" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="质量要求">
            <el-slider
              v-model="extractionConfig.qualityThreshold"
              :min="0.5"
              :max="1.0"
              :step="0.1"
              show-stops
              show-tooltip
            />
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 操作按钮 -->
      <div class="upload-actions" v-if="fileList.length > 0">
        <el-button @click="clearFiles">清空文件</el-button>
        <el-button @click="uploadFiles" :loading="uploading">上传文件</el-button>
        <el-button 
          @click="startExtraction" 
          type="primary" 
          :loading="starting"
          :disabled="!canStartExtraction"
        >
          开始抽取
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

export default {
  name: 'DocumentUploader',
  components: { UploadFilled },
  emits: ['extraction-started'],
  setup(props, { emit }) {
    const uploadRef = ref()
    const fileList = ref([])
    const uploading = ref(false)
    const starting = ref(false)
    const availableTables = ref([])
    const extractionTemplates = ref([])
    
    const extractionConfig = reactive({
      tableId: null,
      templateId: null,
      aiModel: 'qwen-turbo',
      qualityThreshold: 0.8
    })
    
    const acceptedFormats = '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.tiff,.bmp'
    
    const canStartExtraction = computed(() => {
      return fileList.value.length > 0 && 
             extractionConfig.tableId && 
             !uploading.value && 
             !starting.value
    })
    
    const beforeUpload = (file) => {
      const isValidFormat = checkFileFormat(file)
      const isValidSize = file.size <= 50 * 1024 * 1024 // 50MB
      
      if (!isValidFormat) {
        ElMessage.error('不支持的文件格式')
        return false
      }
      
      if (!isValidSize) {
        ElMessage.error('文件大小不能超过50MB')
        return false
      }
      
      return true
    }
    
    const startExtraction = async () => {
      if (!canStartExtraction.value) return
      
      starting.value = true
      try {
        const response = await api.post('/extraction/jobs', {
          files: fileList.value.map(file => file.path),
          config: extractionConfig
        })
        
        emit('extraction-started', response.data)
        ElMessage.success('抽取任务已创建')
        
        // 清空文件列表
        clearFiles()
        
      } catch (error) {
        ElMessage.error('创建抽取任务失败: ' + error.message)
      } finally {
        starting.value = false
      }
    }
    
    return {
      uploadRef,
      fileList,
      uploading,
      starting,
      availableTables,
      extractionTemplates,
      extractionConfig,
      acceptedFormats,
      canStartExtraction,
      beforeUpload,
      startExtraction
    }
  }
}
</script>
```

### 2. ExtractionJobMonitor - 抽取任务监控器
实时监控抽取任务进度的组件。

```vue
<template>
  <div class="extraction-job-monitor">
    <el-card header="任务监控">
      <!-- 任务概览 -->
      <div class="job-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总任务数" :value="jobStats.total" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="运行中" :value="jobStats.running" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="已完成" :value="jobStats.completed" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="失败" :value="jobStats.failed" />
          </el-col>
        </el-row>
      </div>
      
      <!-- 任务列表 -->
      <div class="job-list">
        <el-table :data="jobs" v-loading="loading">
          <el-table-column prop="jobName" label="任务名称" />
          <el-table-column prop="totalDocuments" label="文档数量" width="100" />
          <el-table-column label="进度" width="200">
            <template #default="{ row }">
              <el-progress 
                :percentage="getProgressPercentage(row)"
                :status="getProgressStatus(row)"
              />
              <div class="progress-text">
                {{ row.processedDocuments }}/{{ row.totalDocuments }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="预计完成" width="150">
            <template #default="{ row }">
              {{ formatEstimatedTime(row.estimatedCompletion) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button @click="viewJob(row)" size="small" type="text">
                查看
              </el-button>
              <el-button 
                v-if="row.status === 'running'"
                @click="pauseJob(row)" 
                size="small" 
                type="text"
              >
                暂停
              </el-button>
              <el-button 
                v-if="row.status === 'paused'"
                @click="resumeJob(row)" 
                size="small" 
                type="text"
              >
                继续
              </el-button>
              <el-button 
                v-if="['running', 'paused'].includes(row.status)"
                @click="cancelJob(row)" 
                size="small" 
                type="text"
                class="danger"
              >
                取消
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    
    <!-- 任务详情对话框 -->
    <JobDetailDialog
      v-model="showJobDetail"
      :job="selectedJob"
      @refresh="loadJobs"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import JobDetailDialog from './JobDetailDialog.vue'

export default {
  name: 'ExtractionJobMonitor',
  components: { JobDetailDialog },
  setup() {
    const jobs = ref([])
    const loading = ref(false)
    const showJobDetail = ref(false)
    const selectedJob = ref(null)
    const refreshTimer = ref(null)
    
    const jobStats = reactive({
      total: 0,
      running: 0,
      completed: 0,
      failed: 0
    })
    
    const loadJobs = async () => {
      loading.value = true
      try {
        const response = await api.get('/extraction/jobs')
        jobs.value = response.data.content
        updateJobStats()
      } catch (error) {
        ElMessage.error('加载任务列表失败')
      } finally {
        loading.value = false
      }
    }
    
    const updateJobStats = () => {
      jobStats.total = jobs.value.length
      jobStats.running = jobs.value.filter(job => job.status === 'running').length
      jobStats.completed = jobs.value.filter(job => job.status === 'completed').length
      jobStats.failed = jobs.value.filter(job => job.status === 'failed').length
    }
    
    const getProgressPercentage = (job) => {
      if (job.totalDocuments === 0) return 0
      return Math.round((job.processedDocuments / job.totalDocuments) * 100)
    }
    
    const startAutoRefresh = () => {
      refreshTimer.value = setInterval(loadJobs, 5000) // 每5秒刷新
    }
    
    const stopAutoRefresh = () => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value)
        refreshTimer.value = null
      }
    }
    
    onMounted(() => {
      loadJobs()
      startAutoRefresh()
    })
    
    onUnmounted(() => {
      stopAutoRefresh()
    })
    
    return {
      jobs,
      loading,
      jobStats,
      showJobDetail,
      selectedJob,
      loadJobs,
      getProgressPercentage
    }
  }
}
</script>
```

### 3. QualityReviewPanel - 质量审核面板
人工质量审核界面组件。

```vue
<template>
  <div class="quality-review-panel">
    <el-card header="质量审核">
      <!-- 审核任务列表 -->
      <div class="review-queue">
        <h4>待审核任务 ({{ pendingReviews.length }})</h4>
        <el-table :data="pendingReviews" @row-click="selectReview">
          <el-table-column prop="documentName" label="文档名称" />
          <el-table-column prop="qualityScore" label="质量评分" width="100">
            <template #default="{ row }">
              <el-tag :type="getScoreType(row.qualityScore)">
                {{ (row.qualityScore * 100).toFixed(1) }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="reviewType" label="审核类型" width="120" />
          <el-table-column prop="assignedAt" label="分配时间" width="150" />
        </el-table>
      </div>
      
      <!-- 审核详情 -->
      <div class="review-detail" v-if="selectedReview">
        <el-divider>审核详情</el-divider>
        
        <!-- 原始文档预览 -->
        <div class="document-preview">
          <h5>原始文档</h5>
          <DocumentPreview :document-path="selectedReview.documentPath" />
        </div>
        
        <!-- 抽取结果对比 -->
        <div class="extraction-comparison">
          <h5>抽取结果</h5>
          <el-row :gutter="20">
            <el-col :span="12">
              <h6>AI抽取结果</h6>
              <ExtractionResultEditor
                v-model="originalData"
                :schema="tableSchema"
                :readonly="true"
              />
            </el-col>
            <el-col :span="12">
              <h6>审核后结果</h6>
              <ExtractionResultEditor
                v-model="reviewedData"
                :schema="tableSchema"
                :readonly="false"
              />
            </el-col>
          </el-row>
        </div>
        
        <!-- 质量问题 -->
        <div class="quality-issues" v-if="selectedReview.qualityIssues?.length">
          <h5>质量问题</h5>
          <el-tag
            v-for="issue in selectedReview.qualityIssues"
            :key="issue.id"
            :type="getIssueType(issue.type)"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ issue.message }}
          </el-tag>
        </div>
        
        <!-- 审核意见 -->
        <div class="review-comments">
          <h5>审核意见</h5>
          <el-input
            v-model="reviewComments"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见..."
          />
        </div>
        
        <!-- 审核操作 -->
        <div class="review-actions">
          <el-button @click="rejectReview" type="danger">
            拒绝
          </el-button>
          <el-button @click="approveReview" type="success">
            通过
          </el-button>
          <el-button @click="approveWithChanges" type="primary">
            通过（有修改）
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import DocumentPreview from './DocumentPreview.vue'
import ExtractionResultEditor from './ExtractionResultEditor.vue'

export default {
  name: 'QualityReviewPanel',
  components: {
    DocumentPreview,
    ExtractionResultEditor
  },
  setup() {
    const pendingReviews = ref([])
    const selectedReview = ref(null)
    const originalData = ref({})
    const reviewedData = ref({})
    const reviewComments = ref('')
    const tableSchema = ref({})
    
    const selectReview = (review) => {
      selectedReview.value = review
      originalData.value = { ...review.originalData }
      reviewedData.value = { ...review.originalData }
      reviewComments.value = review.reviewComments || ''
      loadTableSchema(review.tableId)
    }
    
    const approveReview = async () => {
      try {
        await api.post(`/reviews/${selectedReview.value.id}/approve`, {
          reviewedData: reviewedData.value,
          comments: reviewComments.value
        })
        
        ElMessage.success('审核通过')
        loadPendingReviews()
        selectedReview.value = null
        
      } catch (error) {
        ElMessage.error('审核操作失败')
      }
    }
    
    const approveWithChanges = async () => {
      try {
        await api.post(`/reviews/${selectedReview.value.id}/approve`, {
          reviewedData: reviewedData.value,
          comments: reviewComments.value,
          hasChanges: true
        })
        
        ElMessage.success('审核通过（已记录修改）')
        loadPendingReviews()
        selectedReview.value = null
        
      } catch (error) {
        ElMessage.error('审核操作失败')
      }
    }
    
    return {
      pendingReviews,
      selectedReview,
      originalData,
      reviewedData,
      reviewComments,
      tableSchema,
      selectReview,
      approveReview,
      approveWithChanges
    }
  }
}
</script>
```

## 🎨 样式设计

### 主题配色
```scss
// 抽取相关色彩
$extraction-primary: #409EFF;
$extraction-success: #67C23A;
$extraction-warning: #E6A23C;
$extraction-danger: #F56C6C;

// 质量评分色彩
$quality-excellent: #67C23A;
$quality-good: #95D475;
$quality-fair: #E6A23C;
$quality-poor: #F56C6C;

// 进度条色彩
$progress-active: #409EFF;
$progress-success: #67C23A;
$progress-exception: #F56C6C;
```

### 响应式布局
```scss
.extraction-workspace {
  @include mobile {
    .job-monitor {
      .el-table {
        font-size: 12px;
      }
      
      .progress-text {
        font-size: 10px;
      }
    }
  }
  
  @include tablet {
    .document-uploader {
      .upload-area {
        height: 200px;
      }
    }
  }
}
```

## 🧪 测试用例

### 组件测试
- 文档上传组件测试
- 任务监控组件测试
- 质量审核组件测试
- 结果查看组件测试
- 响应式布局测试

### 交互测试
- 文件拖拽上传测试
- 实时进度更新测试
- 审核流程测试
- 数据编辑测试
- 导出功能测试

### 性能测试
- 大文件上传性能测试
- 长列表渲染性能测试
- 实时更新性能测试
- 内存使用优化测试

---

**相关文档**:
- [文档处理引擎](./document-processing-engine.md)
- [AI抽取引擎](./ai-extraction-engine.md)
- [批量处理系统](./batch-processing-system.md)
- [质量控制系统](./quality-control-system.md)
