# AI-FDB v0.3 - 前端实现

## 版本说明

v0.3版本在v0.2前端基础上，新增工作空间管理和数据表设计器功能，保持前端架构的一致性。

## 继承v0.2前端架构

v0.3版本完全继承v0.2版本的前端架构：
- **Vue 3 + TypeScript** - 现代化前端框架
- **Element Plus** - UI组件库
- **Vite** - 构建工具
- **Pinia** - 状态管理
- **Vue Router** - 路由管理

详细信息请参考：
- [v0.1前端实现](../v0.1/frontend-implementation.md) - 基础前端架构
- [v0.2前端实现](../v0.2/frontend-implementation.md) - AI功能前端集成

## v0.3新增前端功能

### 1. 路由配置

```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  // 继承v0.1和v0.2的路由
  ...v1Routes,
  ...v2Routes,
  
  // v0.3新增路由
  {
    path: '/workspaces',
    name: 'Workspaces',
    component: () => import('@/views/workspace/WorkspaceList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/workspaces/:id',
    name: 'WorkspaceDetail',
    component: () => import('@/views/workspace/WorkspaceDetail.vue'),
    meta: { requiresAuth: true },
    redirect: '/workspaces/:id/tables'
  },
  {
    path: '/workspaces/:id/tables',
    name: 'DataTables',
    component: () => import('@/views/table/TableList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/workspaces/:id/tables/create',
    name: 'CreateTable',
    component: () => import('@/views/table/TableDesigner.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/workspaces/:id/tables/:tableId',
    name: 'TableDetail',
    component: () => import('@/views/table/TableDetail.vue'),
    meta: { requiresAuth: true }
  }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
```

### 2. 状态管理

```typescript
// stores/workspace.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Workspace, WorkspaceMember } from '@/types/workspace'

export const useWorkspaceStore = defineStore('workspace', () => {
  const workspaces = ref<Workspace[]>([])
  const currentWorkspace = ref<Workspace | null>(null)
  const members = ref<WorkspaceMember[]>([])
  const loading = ref(false)
  
  const currentWorkspaceId = computed(() => currentWorkspace.value?.id)
  
  const fetchWorkspaces = async () => {
    loading.value = true
    try {
      const response = await workspaceApi.getWorkspaces()
      workspaces.value = response.data
    } finally {
      loading.value = false
    }
  }
  
  const setCurrentWorkspace = (workspace: Workspace) => {
    currentWorkspace.value = workspace
  }
  
  const createWorkspace = async (data: CreateWorkspaceRequest) => {
    const response = await workspaceApi.createWorkspace(data)
    workspaces.value.push(response.data)
    return response.data
  }
  
  return {
    workspaces,
    currentWorkspace,
    members,
    loading,
    currentWorkspaceId,
    fetchWorkspaces,
    setCurrentWorkspace,
    createWorkspace
  }
})
```

```typescript
// stores/table.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { DataTable, TableField } from '@/types/table'

export const useTableStore = defineStore('table', () => {
  const tables = ref<DataTable[]>([])
  const currentTable = ref<DataTable | null>(null)
  const fields = ref<TableField[]>([])
  const loading = ref(false)
  
  const fetchTables = async (workspaceId: number) => {
    loading.value = true
    try {
      const response = await tableApi.getTables(workspaceId)
      tables.value = response.data
    } finally {
      loading.value = false
    }
  }
  
  const createTable = async (workspaceId: number, data: CreateTableRequest) => {
    const response = await tableApi.createTable(workspaceId, data)
    tables.value.push(response.data)
    return response.data
  }
  
  const updateTableField = (fieldId: number, updates: Partial<TableField>) => {
    const fieldIndex = fields.value.findIndex(f => f.id === fieldId)
    if (fieldIndex !== -1) {
      fields.value[fieldIndex] = { ...fields.value[fieldIndex], ...updates }
    }
  }
  
  return {
    tables,
    currentTable,
    fields,
    loading,
    fetchTables,
    createTable,
    updateTableField
  }
})
```

### 3. 核心组件

#### 工作空间列表组件

```vue
<!-- views/workspace/WorkspaceList.vue -->
<template>
  <div class="workspace-list">
    <div class="page-header">
      <h1>我的工作空间</h1>
      <el-button @click="showCreateDialog = true" type="primary">
        <el-icon><Plus /></el-icon>
        创建工作空间
      </el-button>
    </div>
    
    <div class="workspace-grid">
      <div 
        v-for="workspace in workspaces" 
        :key="workspace.id"
        class="workspace-card"
        @click="enterWorkspace(workspace)"
      >
        <el-card :body-style="{ padding: '20px' }">
          <div class="workspace-info">
            <h3>{{ workspace.name }}</h3>
            <p>{{ workspace.description }}</p>
            <div class="workspace-meta">
              <span>{{ workspace.tableCount }} 个数据表</span>
              <span>{{ workspace.memberCount }} 个成员</span>
            </div>
          </div>
          <div class="workspace-actions">
            <el-button @click.stop="editWorkspace(workspace)" size="small">
              编辑
            </el-button>
            <el-dropdown @click.stop>
              <el-button size="small">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="manageMembers(workspace)">
                    成员管理
                  </el-dropdown-item>
                  <el-dropdown-item @click="workspaceSettings(workspace)">
                    设置
                  </el-dropdown-item>
                  <el-dropdown-item 
                    @click="deleteWorkspace(workspace)"
                    divided
                    style="color: #f56c6c"
                  >
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-card>
      </div>
    </div>
    
    <!-- 创建工作空间对话框 -->
    <CreateWorkspaceDialog 
      v-model="showCreateDialog"
      @created="handleWorkspaceCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useWorkspaceStore } from '@/stores/workspace'
import CreateWorkspaceDialog from '@/components/workspace/CreateWorkspaceDialog.vue'

const router = useRouter()
const workspaceStore = useWorkspaceStore()

const showCreateDialog = ref(false)

const { workspaces, loading } = storeToRefs(workspaceStore)

onMounted(() => {
  workspaceStore.fetchWorkspaces()
})

const enterWorkspace = (workspace: Workspace) => {
  workspaceStore.setCurrentWorkspace(workspace)
  router.push(`/workspaces/${workspace.id}`)
}

const handleWorkspaceCreated = (workspace: Workspace) => {
  showCreateDialog.value = false
  enterWorkspace(workspace)
}
</script>
```

#### 表设计器组件

```vue
<!-- views/table/TableDesigner.vue -->
<template>
  <div class="table-designer">
    <div class="designer-header">
      <div class="table-info">
        <el-input
          v-model="tableInfo.name"
          placeholder="表名（英文）"
          class="table-name-input"
        />
        <el-input
          v-model="tableInfo.displayName"
          placeholder="显示名称"
          class="table-display-name-input"
        />
        <el-input
          v-model="tableInfo.description"
          type="textarea"
          placeholder="表描述"
          :rows="2"
          class="table-description-input"
        />
      </div>
      <div class="designer-actions">
        <el-button @click="showAIAssistant = true" type="primary">
          <el-icon><MagicStick /></el-icon>
          AI助手
        </el-button>
        <el-button @click="showTemplateSelector = true">
          <el-icon><Document /></el-icon>
          使用模板
        </el-button>
        <el-button @click="saveTable" type="success" :loading="saving">
          <el-icon><Check /></el-icon>
          保存表结构
        </el-button>
      </div>
    </div>

    <div class="designer-body">
      <div class="fields-panel">
        <div class="panel-header">
          <h3>字段列表</h3>
          <el-button @click="addField" type="primary" size="small">
            <el-icon><Plus /></el-icon>
            添加字段
          </el-button>
        </div>

        <draggable
          v-model="fields"
          @end="handleFieldReorder"
          item-key="id"
          class="field-list"
        >
          <template #item="{ element: field, index }">
            <FieldItem
              :field="field"
              :index="index"
              :selected="selectedFieldIndex === index"
              @select="selectField"
              @edit="editField"
              @delete="deleteField"
            />
          </template>
        </draggable>
      </div>

      <div class="field-editor-panel" v-if="selectedFieldIndex !== -1">
        <FieldEditor
          :field="fields[selectedFieldIndex]"
          @update="updateField"
          @generate-prompt="generateExtractionPrompt"
        />
      </div>
    </div>

    <!-- AI助手对话框 -->
    <AIAssistantDialog
      v-model="showAIAssistant"
      :table-info="tableInfo"
      :existing-fields="fields"
      @apply-suggestions="applySuggestions"
    />

    <!-- 模板选择器 -->
    <TemplateSelector
      v-model="showTemplateSelector"
      @apply-template="applyTemplate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTableStore } from '@/stores/table'
import draggable from 'vuedraggable'
import FieldItem from '@/components/table/FieldItem.vue'
import FieldEditor from '@/components/table/FieldEditor.vue'
import AIAssistantDialog from '@/components/ai/AIAssistantDialog.vue'
import TemplateSelector from '@/components/template/TemplateSelector.vue'

const route = useRoute()
const router = useRouter()
const tableStore = useTableStore()

const workspaceId = Number(route.params.id)

const tableInfo = reactive({
  name: '',
  displayName: '',
  description: ''
})

const fields = ref<TableField[]>([])
const selectedFieldIndex = ref(-1)
const showAIAssistant = ref(false)
const showTemplateSelector = ref(false)
const saving = ref(false)

const addField = () => {
  const newField: TableField = {
    id: Date.now(),
    fieldName: `field_${fields.value.length + 1}`,
    displayName: '',
    fieldType: 'text',
    isRequired: false,
    isUnique: false,
    fieldOrder: fields.value.length,
    fieldOptions: {},
    validationRules: {},
    extractionPrompt: ''
  }
  
  fields.value.push(newField)
  selectedFieldIndex.value = fields.value.length - 1
}

const selectField = (index: number) => {
  selectedFieldIndex.value = index
}

const updateField = (updatedField: TableField) => {
  if (selectedFieldIndex.value !== -1) {
    fields.value[selectedFieldIndex.value] = { ...updatedField }
  }
}

const saveTable = async () => {
  saving.value = true
  try {
    const tableData = {
      ...tableInfo,
      fields: fields.value
    }
    
    await tableStore.createTable(workspaceId, tableData)
    ElMessage.success('表结构保存成功')
    router.push(`/workspaces/${workspaceId}/tables`)
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}
</script>
```

#### 字段编辑器组件

```vue
<!-- components/table/FieldEditor.vue -->
<template>
  <div class="field-editor">
    <el-form :model="fieldForm" :rules="fieldRules" label-width="100px">
      <el-form-item label="字段名" prop="fieldName">
        <el-input v-model="fieldForm.fieldName" placeholder="field_name" />
      </el-form-item>
      
      <el-form-item label="显示名" prop="displayName">
        <el-input v-model="fieldForm.displayName" placeholder="字段显示名称" />
      </el-form-item>
      
      <el-form-item label="字段类型" prop="fieldType">
        <el-select v-model="fieldForm.fieldType" @change="handleTypeChange">
          <el-option label="文本" value="text" />
          <el-option label="数字" value="number" />
          <el-option label="日期" value="date" />
          <el-option label="日期时间" value="datetime" />
          <el-option label="布尔" value="boolean" />
          <el-option label="文件" value="file" />
          <el-option label="URL" value="url" />
          <el-option label="邮箱" value="email" />
          <el-option label="电话" value="phone" />
          <el-option label="单选" value="select" />
          <el-option label="多选" value="multiselect" />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-checkbox v-model="fieldForm.isRequired">必填字段</el-checkbox>
        <el-checkbox v-model="fieldForm.isUnique">唯一值</el-checkbox>
      </el-form-item>
      
      <!-- 字段选项配置 -->
      <FieldOptions 
        :field-type="fieldForm.fieldType"
        v-model="fieldForm.fieldOptions"
      />
      
      <!-- 验证规则配置 -->
      <ValidationRules 
        v-model="fieldForm.validationRules"
      />
      
      <el-form-item label="抽取提示词">
        <el-input 
          v-model="fieldForm.extractionPrompt"
          type="textarea"
          :rows="3"
          placeholder="AI抽取该字段时使用的提示词"
        />
        <el-button 
          @click="generatePrompt"
          size="small"
          type="primary"
          style="margin-top: 8px"
          :loading="generatingPrompt"
        >
          AI生成提示词
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { TableField } from '@/types/table'
import FieldOptions from './FieldOptions.vue'
import ValidationRules from './ValidationRules.vue'
import { aiService } from '@/services/aiService'

const props = defineProps<{
  field: TableField
}>()

const emit = defineEmits<{
  update: [field: TableField]
  generatePrompt: [prompt: string]
}>()

const fieldForm = reactive<TableField>({ ...props.field })
const generatingPrompt = ref(false)

const fieldRules = {
  fieldName: [
    { required: true, message: '请输入字段名', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '字段名只能包含字母、数字和下划线，且不能以数字开头', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  fieldType: [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ]
}

watch(fieldForm, (newValue) => {
  emit('update', { ...newValue })
}, { deep: true })

const handleTypeChange = () => {
  // 根据字段类型重置选项配置
  fieldForm.fieldOptions = {}
  fieldForm.validationRules = {}
}

const generatePrompt = async () => {
  generatingPrompt.value = true
  try {
    const prompt = await aiService.generateExtractionPrompt({
      fieldName: fieldForm.fieldName,
      displayName: fieldForm.displayName,
      fieldType: fieldForm.fieldType,
      context: '从文档中抽取该字段的值'
    })
    
    fieldForm.extractionPrompt = prompt
    emit('generatePrompt', prompt)
    ElMessage.success('提示词生成成功')
  } catch (error) {
    ElMessage.error('提示词生成失败: ' + error.message)
  } finally {
    generatingPrompt.value = false
  }
}
</script>
```

### 4. 类型定义

```typescript
// types/workspace.ts
export interface Workspace {
  id: number
  name: string
  description?: string
  ownerId: number
  settings?: Record<string, any>
  status: 'active' | 'archived'
  tableCount?: number
  memberCount?: number
  createdAt: string
  updatedAt: string
}

export interface WorkspaceMember {
  id: number
  workspaceId: number
  userId: number
  role: 'owner' | 'admin' | 'editor' | 'viewer'
  permissions?: Record<string, any>
  joinedAt: string
  user?: {
    id: number
    username: string
    email: string
    fullName?: string
  }
}

export interface CreateWorkspaceRequest {
  name: string
  description?: string
}
```

```typescript
// types/table.ts
export interface DataTable {
  id: number
  name: string
  displayName?: string
  description?: string
  workspaceId: number
  tableSchema: Record<string, any>
  aiConfig?: Record<string, any>
  version: number
  status: 'draft' | 'active' | 'archived'
  createdBy: number
  createdAt: string
  updatedAt: string
}

export interface TableField {
  id: number
  tableId?: number
  fieldName: string
  displayName?: string
  fieldType: FieldType
  isRequired: boolean
  isUnique: boolean
  fieldOrder: number
  defaultValue?: string
  fieldOptions: Record<string, any>
  validationRules: Record<string, any>
  extractionPrompt?: string
  extractionExamples?: any[]
}

export type FieldType = 
  | 'text' 
  | 'number' 
  | 'date' 
  | 'datetime' 
  | 'boolean' 
  | 'file' 
  | 'url' 
  | 'email' 
  | 'phone' 
  | 'select' 
  | 'multiselect'

export interface CreateTableRequest {
  name: string
  displayName?: string
  description?: string
  fields: Omit<TableField, 'id' | 'tableId'>[]
}
```

### 5. API服务

```typescript
// services/workspaceApi.ts
import { http } from '@/utils/http'
import type { Workspace, CreateWorkspaceRequest } from '@/types/workspace'

export const workspaceApi = {
  getWorkspaces(): Promise<ApiResponse<Workspace[]>> {
    return http.get('/api/workspaces')
  },
  
  createWorkspace(data: CreateWorkspaceRequest): Promise<ApiResponse<Workspace>> {
    return http.post('/api/workspaces', data)
  },
  
  updateWorkspace(id: number, data: Partial<Workspace>): Promise<ApiResponse<Workspace>> {
    return http.put(`/api/workspaces/${id}`, data)
  },
  
  deleteWorkspace(id: number): Promise<ApiResponse<void>> {
    return http.delete(`/api/workspaces/${id}`)
  }
}
```

```typescript
// services/tableApi.ts
import { http } from '@/utils/http'
import type { DataTable, CreateTableRequest } from '@/types/table'

export const tableApi = {
  getTables(workspaceId: number): Promise<ApiResponse<DataTable[]>> {
    return http.get(`/api/workspaces/${workspaceId}/tables`)
  },
  
  createTable(workspaceId: number, data: CreateTableRequest): Promise<ApiResponse<DataTable>> {
    return http.post(`/api/workspaces/${workspaceId}/tables`, data)
  },
  
  updateTable(workspaceId: number, tableId: number, data: Partial<DataTable>): Promise<ApiResponse<DataTable>> {
    return http.put(`/api/workspaces/${workspaceId}/tables/${tableId}`, data)
  },
  
  deleteTable(workspaceId: number, tableId: number): Promise<ApiResponse<void>> {
    return http.delete(`/api/workspaces/${workspaceId}/tables/${tableId}`)
  }
}
```
