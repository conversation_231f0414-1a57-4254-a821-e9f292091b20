# AI-FDB v0.2 - 数据库设计

## 数据库架构概述

v0.2版本在继承v0.1用户认证相关表的基础上，新增OCR文档识别和AI语义抽取相关的数据表。数据库设计遵循第三范式，确保数据一致性和完整性，同时针对OCR和AI处理的特点进行了性能优化。

## 🗄️ 数据库结构图

```
AI-FDB v0.2 数据库结构:

继承v0.1表:
├── users (用户表)
├── user_sessions (用户会话表)
└── system_configs (系统配置表)

v0.2新增表:
├── documents (文档基础信息表)
├── ocr_results (OCR识别结果表)
├── semantic_extractions (语义抽取记录表)
├── extraction_field_configs (字段抽取配置表)
├── document_processing_tasks (文档处理任务表)
└── processing_quality_metrics (处理质量评估表)
```

## 📋 新增数据表设计

### 1. 文档基础信息表 (documents)

存储上传文档的基础信息和元数据。

```sql
CREATE TABLE documents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文档ID，主键',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型(MIME)',
    file_extension VARCHAR(10) NOT NULL COMMENT '文件扩展名',
    file_hash VARCHAR(64) COMMENT '文件MD5哈希值',
    page_count INT COMMENT '页数(PDF等多页文档)',
    upload_source VARCHAR(50) DEFAULT 'web' COMMENT '上传来源',
    description TEXT COMMENT '文档描述',
    tags JSON COMMENT '文档标签',
    status ENUM('uploaded', 'processing', 'completed', 'failed', 'deleted') DEFAULT 'uploaded' COMMENT '文档状态',
    created_by BIGINT NOT NULL COMMENT '上传用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 索引设计
    INDEX idx_created_by (created_by),
    INDEX idx_file_type (file_type),
    INDEX idx_status (status),
    INDEX idx_file_hash (file_hash),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_user_status (created_by, status),
    INDEX idx_type_status (file_type, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档基础信息表';
```

### 2. OCR识别结果表 (ocr_results)

存储PaddleOCR识别的详细结果和元数据。

```sql
CREATE TABLE ocr_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'OCR结果ID，主键',
    document_id BIGINT NOT NULL COMMENT '关联文档ID',
    page_number INT DEFAULT 1 COMMENT '页码(多页文档)',
    ocr_engine VARCHAR(50) DEFAULT 'PaddleOCR' COMMENT 'OCR引擎名称',
    engine_version VARCHAR(50) DEFAULT 'PP-OCRv5_server_rec' COMMENT '引擎版本',
    raw_text LONGTEXT COMMENT '原始OCR识别文本',
    structured_result JSON COMMENT '结构化OCR结果(包含坐标、置信度等)',
    text_blocks JSON COMMENT '文本块详细信息',
    confidence_score DECIMAL(5,3) COMMENT 'OCR识别平均置信度(0-1)',
    block_count INT COMMENT '识别的文本块数量',
    processing_time INT COMMENT '处理时间(毫秒)',
    processing_config JSON COMMENT '处理配置参数',
    language VARCHAR(10) DEFAULT 'ch' COMMENT '识别语言',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '处理状态',
    error_message TEXT COMMENT '错误信息',
    created_by BIGINT NOT NULL COMMENT '处理用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    -- 索引设计
    INDEX idx_document_id (document_id),
    INDEX idx_status (status),
    INDEX idx_confidence (confidence_score),
    INDEX idx_created_at (created_at),
    INDEX idx_page_number (page_number),
    
    -- 复合索引
    INDEX idx_doc_page (document_id, page_number),
    INDEX idx_doc_status (document_id, status),
    INDEX idx_engine_version (ocr_engine, engine_version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OCR识别结果表';
```

### 3. 语义抽取记录表 (semantic_extractions)

存储通义千问AI语义抽取的结果和配置。

```sql
CREATE TABLE semantic_extractions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '语义抽取ID，主键',
    ocr_result_id BIGINT NOT NULL COMMENT '关联OCR结果ID',
    document_id BIGINT NOT NULL COMMENT '关联文档ID',
    source_text LONGTEXT NOT NULL COMMENT '用于抽取的源文本',
    extraction_fields JSON NOT NULL COMMENT '抽取字段配置',
    extracted_data JSON COMMENT '抽取的结构化数据',
    field_confidence JSON COMMENT '各字段抽取置信度',
    extraction_notes TEXT COMMENT '抽取过程说明',
    ai_model VARCHAR(100) DEFAULT 'qwen-turbo' COMMENT 'AI模型名称',
    model_version VARCHAR(50) COMMENT '模型版本',
    processing_time INT COMMENT '处理时间(毫秒)',
    token_usage JSON COMMENT 'Token使用统计',
    temperature DECIMAL(3,2) DEFAULT 0.3 COMMENT '模型温度参数',
    max_tokens INT DEFAULT 4000 COMMENT '最大Token数',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '处理状态',
    error_message TEXT COMMENT '错误信息',
    raw_response LONGTEXT COMMENT 'AI原始响应',
    created_by BIGINT NOT NULL COMMENT '处理用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (ocr_result_id) REFERENCES ocr_results(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    -- 索引设计
    INDEX idx_ocr_result_id (ocr_result_id),
    INDEX idx_document_id (document_id),
    INDEX idx_status (status),
    INDEX idx_ai_model (ai_model),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_doc_status (document_id, status),
    INDEX idx_model_status (ai_model, status),
    INDEX idx_user_created (created_by, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='语义抽取记录表';
```

### 4. 字段抽取配置表 (extraction_field_configs)

存储用户自定义的字段抽取配置模板。

```sql
CREATE TABLE extraction_field_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID，主键',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    field_name VARCHAR(100) NOT NULL COMMENT '字段名称',
    field_description TEXT COMMENT '字段描述',
    extraction_prompt TEXT NOT NULL COMMENT '抽取提示词',
    field_type ENUM('text', 'number', 'date', 'boolean', 'list', 'object') DEFAULT 'text' COMMENT '字段类型',
    validation_rules JSON COMMENT '字段验证规则',
    default_value VARCHAR(255) COMMENT '默认值',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    category VARCHAR(50) DEFAULT 'general' COMMENT '字段分类',
    usage_count INT DEFAULT 0 COMMENT '使用次数统计',
    success_rate DECIMAL(5,3) COMMENT '抽取成功率',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开配置',
    created_by BIGINT NOT NULL COMMENT '创建用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    -- 索引设计
    INDEX idx_field_name (field_name),
    INDEX idx_config_name (config_name),
    INDEX idx_category (category),
    INDEX idx_is_active (is_active),
    INDEX idx_is_public (is_public),
    INDEX idx_created_by (created_by),
    
    -- 复合索引
    INDEX idx_active_public (is_active, is_public),
    INDEX idx_category_active (category, is_active),
    INDEX idx_user_active (created_by, is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字段抽取配置表';
```

### 5. 文档处理任务表 (document_processing_tasks)

存储文档处理任务的状态和进度信息。

```sql
CREATE TABLE document_processing_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID，主键',
    document_id BIGINT NOT NULL COMMENT '关联文档ID',
    task_type ENUM('ocr', 'extraction', 'full_process') NOT NULL COMMENT '任务类型',
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_config JSON COMMENT '任务配置参数',
    priority INT DEFAULT 5 COMMENT '任务优先级(1-10)',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    progress INT DEFAULT 0 COMMENT '任务进度(0-100)',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    processing_time INT COMMENT '处理时长(毫秒)',
    result_summary JSON COMMENT '结果摘要',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retries INT DEFAULT 3 COMMENT '最大重试次数',
    worker_id VARCHAR(100) COMMENT '处理节点ID',
    created_by BIGINT NOT NULL COMMENT '创建用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    -- 索引设计
    INDEX idx_document_id (document_id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at),
    INDEX idx_worker_id (worker_id),
    
    -- 复合索引
    INDEX idx_status_priority (status, priority),
    INDEX idx_type_status (task_type, status),
    INDEX idx_doc_type (document_id, task_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档处理任务表';
```

### 6. 处理质量评估表 (processing_quality_metrics)

存储OCR和AI处理质量的评估指标。

```sql
CREATE TABLE processing_quality_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评估ID，主键',
    document_id BIGINT NOT NULL COMMENT '关联文档ID',
    ocr_result_id BIGINT COMMENT '关联OCR结果ID',
    extraction_id BIGINT COMMENT '关联抽取结果ID',
    metric_type ENUM('ocr_quality', 'extraction_quality', 'overall_quality') NOT NULL COMMENT '评估类型',
    quality_score DECIMAL(5,3) COMMENT '质量评分(0-1)',
    accuracy_score DECIMAL(5,3) COMMENT '准确率评分',
    completeness_score DECIMAL(5,3) COMMENT '完整性评分',
    confidence_score DECIMAL(5,3) COMMENT '置信度评分',
    processing_speed_score DECIMAL(5,3) COMMENT '处理速度评分',
    detailed_metrics JSON COMMENT '详细评估指标',
    evaluation_notes TEXT COMMENT '评估说明',
    evaluator_type ENUM('auto', 'manual', 'hybrid') DEFAULT 'auto' COMMENT '评估方式',
    reference_data JSON COMMENT '参考数据',
    created_by BIGINT NOT NULL COMMENT '评估用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (ocr_result_id) REFERENCES ocr_results(id) ON DELETE SET NULL,
    FOREIGN KEY (extraction_id) REFERENCES semantic_extractions(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    -- 索引设计
    INDEX idx_document_id (document_id),
    INDEX idx_metric_type (metric_type),
    INDEX idx_quality_score (quality_score),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_doc_type (document_id, metric_type),
    INDEX idx_type_score (metric_type, quality_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='处理质量评估表';
```

## 🔧 数据库初始化脚本

### 默认字段配置数据

```sql
-- 插入默认字段抽取配置
INSERT INTO extraction_field_configs (
    config_name, field_name, field_description, extraction_prompt, 
    field_type, category, is_public, created_by
) VALUES
-- 通用字段
('通用-文档标题', '文档标题', '文档的标题或名称', '从文档中提取标题、文档名称或主要标识信息，返回完整标题', 'text', 'general', true, 1),
('通用-日期信息', '日期', '文档中的日期信息', '从文档中提取日期信息，包括创建日期、打印日期、生效日期等，格式为YYYY-MM-DD或YYYY年MM月DD日', 'date', 'general', true, 1),
('通用-金额数值', '金额', '文档中的金额数值', '从文档中提取金额、价格或费用，包含数字和货币单位，如"1000元"、"$500"', 'number', 'general', true, 1),

-- 身份证件字段
('证件-姓名', '姓名', '证件持有人姓名', '从证件中提取持有人姓名、所有者姓名或注册人姓名，返回完整中文姓名', 'text', 'identity', true, 1),
('证件-证件号码', '证件号码', '身份证、护照等证件号码', '从文档中提取身份证号码、护照号码或其他证件号码，保持原始格式', 'text', 'identity', true, 1),
('证件-发证机关', '发证机关', '证件的发证机关', '从证件中提取发证机关、签发单位或颁发部门的名称', 'text', 'identity', true, 1),

-- 合同文档字段
('合同-甲方名称', '甲方名称', '合同甲方的名称', '从合同中提取甲方名称、甲方公司名称或甲方个人姓名', 'text', 'contract', true, 1),
('合同-乙方名称', '乙方名称', '合同乙方的名称', '从合同中提取乙方名称、乙方公司名称或乙方个人姓名', 'text', 'contract', true, 1),
('合同-合同金额', '合同金额', '合同涉及的金额', '从合同中提取合同金额、总价、费用等数值信息，包含数字和货币单位', 'number', 'contract', true, 1),
('合同-签署日期', '签署日期', '合同签署日期', '从合同中提取签署日期、签订日期或生效日期，格式为YYYY-MM-DD', 'date', 'contract', true, 1),

-- 发票字段
('发票-发票号码', '发票号码', '发票的号码', '从发票中提取发票号码、发票代码或发票编号', 'text', 'invoice', true, 1),
('发票-开票日期', '开票日期', '发票开具日期', '从发票中提取开票日期、开具日期，格式为YYYY-MM-DD', 'date', 'invoice', true, 1),
('发票-税额', '税额', '发票中的税额', '从发票中提取税额、增值税额等税费信息，包含数字和货币单位', 'number', 'invoice', true, 1),

-- 房产证字段
('房产-房屋地址', '房屋地址', '房产的详细地址', '从房产证中提取房屋坐落地址、房产地址或物业地址', 'text', 'property', true, 1),
('房产-建筑面积', '建筑面积', '房屋建筑面积', '从房产证中提取建筑面积、房屋面积，包含数字和单位(平方米)', 'number', 'property', true, 1),
('房产-权利人', '权利人', '房产权利人姓名', '从房产证中提取权利人姓名、产权人姓名或所有权人姓名', 'text', 'property', true, 1);
```

## 📊 数据库性能优化

### 索引优化策略

```sql
-- OCR结果查询优化
CREATE INDEX idx_ocr_doc_confidence ON ocr_results(document_id, confidence_score DESC);
CREATE INDEX idx_ocr_status_time ON ocr_results(status, created_at DESC);

-- 语义抽取查询优化
CREATE INDEX idx_extraction_doc_time ON semantic_extractions(document_id, created_at DESC);
CREATE INDEX idx_extraction_model_status ON semantic_extractions(ai_model, status);

-- 任务处理查询优化
CREATE INDEX idx_task_status_priority ON document_processing_tasks(status, priority DESC, created_at);
CREATE INDEX idx_task_worker_status ON document_processing_tasks(worker_id, status);

-- 质量评估查询优化
CREATE INDEX idx_quality_doc_type ON processing_quality_metrics(document_id, metric_type, quality_score DESC);
```

### 分区策略

```sql
-- 按时间分区OCR结果表(按月分区)
ALTER TABLE ocr_results PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 按时间分区语义抽取表(按月分区)
ALTER TABLE semantic_extractions PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 🔄 数据迁移和维护

### 数据清理策略

```sql
-- 清理过期的处理任务(30天前)
DELETE FROM document_processing_tasks 
WHERE status IN ('completed', 'failed', 'cancelled') 
AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理临时OCR结果(7天前的失败记录)
DELETE FROM ocr_results 
WHERE status = 'failed' 
AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 更新字段配置使用统计
UPDATE extraction_field_configs 
SET usage_count = (
    SELECT COUNT(*) FROM semantic_extractions se
    WHERE JSON_CONTAINS(se.extraction_fields, JSON_OBJECT('field_name', extraction_field_configs.field_name))
);
```

### 备份策略

```sql
-- 每日备份脚本
mysqldump --single-transaction --routines --triggers \
  --where="created_at >= CURDATE() - INTERVAL 1 DAY" \
  aifdb_dev documents ocr_results semantic_extractions > daily_backup.sql

-- 每周全量备份
mysqldump --single-transaction --routines --triggers \
  aifdb_dev > weekly_full_backup.sql
```

这个数据库设计确保了v0.2版本能够高效存储和管理OCR识别结果、AI语义抽取数据，同时保持与v0.1版本的完全兼容性。
