@echo off
echo ========================================
echo AI-FDB Project Folder Sharing Setup
echo Local IP: *************
echo ========================================
echo.

REM Check administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Error: Administrator privileges required
    echo Please right-click Command Prompt and select "Run as administrator"
    pause
    exit /b 1
)

REM Set project path
set PROJECT_PATH=E:\AI-FDB
set SHARE_NAME=AI-FDB

echo Project Path: %PROJECT_PATH%
echo Share Name: %SHARE_NAME%
echo.

REM Check if project path exists
if not exist "%PROJECT_PATH%" (
    echo Error: Project path %PROJECT_PATH% does not exist
    echo Please confirm AI-FDB project path is correct
    pause
    exit /b 1
)

echo [1/5] Removing existing share (if any)...
net share %SHARE_NAME% /delete >nul 2>&1
echo + Cleanup complete
echo.

echo [2/5] Creating AI-FDB project share...
net share %SHARE_NAME%="%PROJECT_PATH%" /grant:everyone,change
if %errorLevel% equ 0 (
    echo + Share created successfully
) else (
    echo - Share creation failed
    pause
    exit /b 1
)
echo.

echo [3/5] Setting folder permissions...
REM Grant Everyone modify permissions
icacls "%PROJECT_PATH%" /grant Everyone:(OI)(CI)M >nul 2>&1
if %errorLevel% equ 0 (
    echo + Folder permissions set successfully
) else (
    echo ! Folder permissions may have failed, but share is still usable
)
echo.

echo [4/5] Enabling network discovery and sharing...
netsh advfirewall firewall set rule group="Network Discovery" new enable=Yes >nul 2>&1
netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=Yes >nul 2>&1
echo + Network discovery and sharing enabled
echo.

echo [5/5] Verifying share settings...
net share | findstr %SHARE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    echo + Share verification successful
) else (
    echo - Share verification failed
)
echo.

echo ========================================
echo Share Setup Complete!
echo ========================================
echo.
echo AI-FDB project can now be accessed via:
echo.
echo LAN Access Paths:
echo - \\%COMPUTERNAME%\%SHARE_NAME%
echo - \\*************\%SHARE_NAME%
echo.
echo Access Methods:
echo 1. Enter above paths in File Explorer address bar
echo 2. Enter above paths in Run dialog (Win+R)
echo 3. Enter paths in other devices' file managers
echo.
echo Permission Details:
echo - All users can read, write and modify files
echo - Can create new files and folders
echo - Can delete files (please use caution)
echo.
echo Test Commands:
echo Local test: net view \\%COMPUTERNAME%
echo Remote test: net view \\*************
echo.
echo Notes:
echo - Ensure all devices are in 192.168.124.x network segment
echo - If access fails, check firewall settings
echo - Recommend regular backup of important files
echo.

REM Display all current shares
echo Current share list:
net share
echo.

echo To remove share, run:
echo net share %SHARE_NAME% /delete
echo.
pause