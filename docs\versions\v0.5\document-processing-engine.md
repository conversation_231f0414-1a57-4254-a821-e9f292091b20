# AI-FDB v0.5 - 文档处理引擎

## 概述

文档处理引擎是v0.5版本的核心组件，负责处理多种格式的文档，包括PDF、Word、Excel、图片等，并集成OCR服务进行文字识别。本模块为后续的AI语义抽取提供标准化的文本内容。

## 🎯 功能目标

- **多格式支持** - PDF、Word、Excel、PowerPoint、图片、扫描件等
- **OCR集成** - 百度OCR、腾讯OCR等多种OCR服务
- **内容提取** - 文本、表格、图像的结构化提取
- **预处理优化** - 噪声去除、版面分析、格式标准化
- **批量处理** - 大批量文档的高效处理
- **质量控制** - 处理结果的质量评估和优化

## 🏗️ 系统架构

### 文档处理流程
```
文档上传 → 格式识别 → 内容解析 → OCR识别 → 结构化提取 → 质量检查 → 结果输出
    ↓        ↓        ↓        ↓         ↓         ↓        ↓
  存储管理  格式检测  解析引擎  OCR服务   内容提取   质量评估  标准输出
```

### 核心组件
1. **DocumentProcessor** - 文档处理主控制器
2. **FormatDetector** - 文档格式检测器
3. **ContentExtractor** - 内容提取器
4. **OCRService** - OCR服务集成
5. **LayoutAnalyzer** - 版面分析器
6. **QualityAssessor** - 质量评估器

## 📊 支持的文档格式

### PDF文档
- **文本PDF** - 直接提取文本内容
- **图像PDF** - OCR识别扫描件内容
- **混合PDF** - 文本和图像混合处理
- **表格PDF** - 表格结构识别和提取

### Office文档
- **Word文档** - .doc/.docx格式支持
- **Excel文档** - .xls/.xlsx格式支持
- **PowerPoint** - .ppt/.pptx格式支持
- **文本文档** - .txt/.rtf格式支持

### 图像文件
- **常见格式** - JPG、PNG、TIFF、BMP
- **扫描件** - 高分辨率扫描文档
- **截图** - 屏幕截图和手机拍照
- **多页图像** - TIFF多页文档

## 🔧 技术实现

### 文档处理配置
```yaml
# application.yml
document:
  processing:
    max-file-size: 100MB
    supported-formats:
      - pdf
      - doc
      - docx
      - xls
      - xlsx
      - ppt
      - pptx
      - jpg
      - png
      - tiff
      - bmp
    temp-dir: ${java.io.tmpdir}/ai-fdb/processing
    
  ocr:
    providers:
      baidu:
        app-id: ${BAIDU_OCR_APP_ID}
        api-key: ${BAIDU_OCR_API_KEY}
        secret-key: ${BAIDU_OCR_SECRET_KEY}
      tencent:
        secret-id: ${TENCENT_OCR_SECRET_ID}
        secret-key: ${TENCENT_OCR_SECRET_KEY}
        region: ap-beijing
    
  quality:
    min-confidence: 0.8
    max-retry-attempts: 3
    enable-auto-correction: true
```

### 文档处理器实现
```java
@Service
public class DocumentProcessor {

    @Autowired
    private FormatDetector formatDetector;
    
    @Autowired
    private ContentExtractorFactory extractorFactory;
    
    @Autowired
    private OCRServiceManager ocrServiceManager;

    public ProcessingResult processDocument(String filePath) {
        try {
            // 1. 检测文档格式
            DocumentFormat format = formatDetector.detectFormat(filePath);
            
            // 2. 选择合适的提取器
            ContentExtractor extractor = extractorFactory.getExtractor(format);
            
            // 3. 提取内容
            ExtractedContent content = extractor.extract(filePath);
            
            // 4. OCR处理（如果需要）
            if (content.requiresOCR()) {
                content = performOCR(content);
            }
            
            // 5. 质量评估
            QualityScore quality = assessQuality(content);
            
            // 6. 构建结果
            return ProcessingResult.builder()
                .content(content)
                .quality(quality)
                .format(format)
                .processingTime(System.currentTimeMillis() - startTime)
                .build();
                
        } catch (Exception e) {
            log.error("文档处理失败: {}", filePath, e);
            throw new DocumentProcessingException("文档处理失败", e);
        }
    }

    private ExtractedContent performOCR(ExtractedContent content) {
        OCRService ocrService = ocrServiceManager.getBestService(content.getImageType());
        
        List<OCRResult> ocrResults = new ArrayList<>();
        for (DocumentImage image : content.getImages()) {
            OCRResult result = ocrService.recognize(image);
            ocrResults.add(result);
        }
        
        return content.withOCRResults(ocrResults);
    }
}
```

### PDF处理器
```java
@Component
public class PDFContentExtractor implements ContentExtractor {

    @Override
    public ExtractedContent extract(String filePath) {
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            
            ExtractedContent.Builder builder = ExtractedContent.builder();
            
            // 提取文本内容
            PDFTextStripper textStripper = new PDFTextStripper();
            String text = textStripper.getText(document);
            builder.text(text);
            
            // 提取图像
            List<DocumentImage> images = extractImages(document);
            builder.images(images);
            
            // 提取表格
            List<DocumentTable> tables = extractTables(document);
            builder.tables(tables);
            
            // 分析版面
            LayoutInfo layout = analyzeLayout(document);
            builder.layout(layout);
            
            return builder.build();
            
        } catch (IOException e) {
            throw new ContentExtractionException("PDF处理失败", e);
        }
    }

    private List<DocumentImage> extractImages(PDDocument document) {
        List<DocumentImage> images = new ArrayList<>();
        
        for (PDPage page : document.getPages()) {
            try {
                PDResources resources = page.getResources();
                for (COSName name : resources.getXObjectNames()) {
                    PDXObject xObject = resources.getXObject(name);
                    if (xObject instanceof PDImageXObject) {
                        PDImageXObject image = (PDImageXObject) xObject;
                        BufferedImage bufferedImage = image.getImage();
                        images.add(new DocumentImage(bufferedImage, page.getPageNumber()));
                    }
                }
            } catch (IOException e) {
                log.warn("提取页面图像失败: {}", page.getPageNumber(), e);
            }
        }
        
        return images;
    }
}
```

### OCR服务管理器
```java
@Service
public class OCRServiceManager {

    @Autowired
    private BaiduOCRService baiduOCRService;
    
    @Autowired
    private TencentOCRService tencentOCRService;

    public OCRService getBestService(ImageType imageType) {
        // 根据图像类型选择最佳OCR服务
        switch (imageType) {
            case IDENTITY_CARD:
                return tencentOCRService; // 腾讯OCR在身份证识别方面更准确
            case INVOICE:
                return baiduOCRService; // 百度OCR在发票识别方面更准确
            case GENERAL_TEXT:
            default:
                return baiduOCRService; // 默认使用百度OCR
        }
    }

    public OCRResult recognizeWithFallback(DocumentImage image) {
        // 主服务识别
        OCRService primaryService = getBestService(image.getType());
        try {
            return primaryService.recognize(image);
        } catch (Exception e) {
            log.warn("主OCR服务失败，尝试备用服务", e);
        }
        
        // 备用服务识别
        OCRService fallbackService = getFallbackService(primaryService);
        try {
            return fallbackService.recognize(image);
        } catch (Exception e) {
            log.error("所有OCR服务都失败", e);
            throw new OCRException("OCR识别失败", e);
        }
    }
}
```

### 百度OCR服务实现
```java
@Service
public class BaiduOCRService implements OCRService {

    @Value("${document.ocr.baidu.app-id}")
    private String appId;
    
    @Value("${document.ocr.baidu.api-key}")
    private String apiKey;
    
    @Value("${document.ocr.baidu.secret-key}")
    private String secretKey;

    @Override
    public OCRResult recognize(DocumentImage image) {
        try {
            // 初始化百度OCR客户端
            AipOcr client = new AipOcr(appId, apiKey, secretKey);
            
            // 设置可选参数
            HashMap<String, String> options = new HashMap<>();
            options.put("language_type", "CHN_ENG");
            options.put("detect_direction", "true");
            options.put("detect_language", "true");
            options.put("probability", "true");
            
            // 转换图像为字节数组
            byte[] imageBytes = convertImageToBytes(image.getBufferedImage());
            
            // 调用通用文字识别
            JSONObject result = client.basicGeneral(imageBytes, options);
            
            // 解析结果
            return parseOCRResult(result);
            
        } catch (Exception e) {
            throw new OCRException("百度OCR识别失败", e);
        }
    }

    private OCRResult parseOCRResult(JSONObject result) {
        OCRResult.Builder builder = OCRResult.builder();
        
        if (result.has("words_result")) {
            JSONArray wordsResult = result.getJSONArray("words_result");
            List<OCRTextBlock> textBlocks = new ArrayList<>();
            
            for (int i = 0; i < wordsResult.length(); i++) {
                JSONObject word = wordsResult.getJSONObject(i);
                String text = word.getString("words");
                
                // 获取置信度（如果有）
                double confidence = 1.0;
                if (word.has("probability")) {
                    JSONObject prob = word.getJSONObject("probability");
                    confidence = prob.getDouble("average");
                }
                
                textBlocks.add(new OCRTextBlock(text, confidence));
            }
            
            builder.textBlocks(textBlocks);
        }
        
        // 设置整体置信度
        double overallConfidence = calculateOverallConfidence(builder.build().getTextBlocks());
        builder.confidence(overallConfidence);
        
        return builder.build();
    }
}
```

## 🎨 质量控制

### 质量评估指标
- **文本识别准确率** - OCR识别的文字准确性
- **结构保持度** - 原文档结构的保持程度
- **完整性** - 内容提取的完整性
- **置信度** - OCR和解析的置信度评分

### 质量优化策略
- **多服务对比** - 使用多个OCR服务对比结果
- **自动纠错** - 基于词典和语法的自动纠错
- **人工校验** - 低置信度结果的人工校验
- **反馈学习** - 基于用户反馈优化处理效果

## 🧪 测试用例

### 功能测试
- 各种文档格式的处理测试
- OCR识别准确性测试
- 表格结构提取测试
- 图像内容识别测试
- 版面分析准确性测试

### 性能测试
- 大文件处理性能测试
- 批量文档处理测试
- OCR服务响应时间测试
- 内存使用优化测试

### 质量测试
- 文字识别准确率测试
- 结构保持度测试
- 不同质量文档的处理测试
- 边界情况处理测试

---

**相关文档**:
- [AI抽取引擎](./ai-extraction-engine.md)
- [批量处理系统](./batch-processing-system.md)
- [质量控制系统](./quality-control-system.md)
